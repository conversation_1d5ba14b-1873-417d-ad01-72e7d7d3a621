
# 🚀 MemeForge - Transform Memes into Reality

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-blue.svg)](https://www.typescriptlang.org/)

## 🎯 Available Product Names (Domain Check Required)

- **MemeForge** - Transform memes into apps
- **ViralCodeLab** - Where memes become reality
- **MemeToApp** - Direct meme-to-application pipeline
- **RedditAppFactory** - Reddit-powered app generation
- **MemeTech** - Technology meets internet culture
- **AppMemizer** - Memefy your app ideas
- **CodeMeme** - Coding meets meme culture
- **ViralAppGen** - Generate apps from viral content

## 🏗️ System Architecture

```mermaid
graph TB
    A[User Interface] --> B[Meme Browser]
    A --> C[AI Analysis Engine]
    A --> D[Community Hub]
    
    B --> E[Reddit API Integration]
    B --> F[Meme Categorization]
    
    C --> G[Concept Extraction]
    C --> H[App Blueprint Generator]
    C --> I[Code Template Engine]
    
    D --> J[Voting System]
    D --> K[Comments & Reviews]
    D --> L[Hall of Fame]
    
    E --> M[Trending Memes]
    E --> N[Viral Content Feed]
    
    G --> O[AI Analysis Results]
    H --> P[Generated App Concepts]
    I --> Q[Downloadable Code]
    
    J --> R[Community Rankings]
    K --> S[User Feedback]
    L --> T[Success Stories]
```

## 🔄 Workflow Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant MB as Meme Browser
    participant AI as AI Engine
    participant C as Community
    participant HF as Hall of Fame
    
    U->>MB: Browse trending memes
    MB->>U: Display meme feed
    U->>MB: Select meme for analysis
    MB->>AI: Send meme data
    AI->>AI: Extract concepts & themes
    AI->>U: Generate app blueprint
    U->>C: Share generated concept
    C->>C: Community votes & comments
    C->>HF: Promote successful concepts
    HF->>U: Showcase top transformations
```

## 📁 Project Structure

```
src/
├── components/
│   ├── layout/
│   │   ├── Header.tsx
│   │   ├── Navigation.tsx
│   │   └── Footer.tsx
│   ├── memes/
│   │   ├── MemeCard.tsx
│   │   ├── MemeGrid.tsx
│   │   ├── MemeAnalyzer.tsx
│   │   └── TrendingMemes.tsx
│   ├── ai/
│   │   ├── ConceptGenerator.tsx
│   │   ├── AppBlueprint.tsx
│   │   └── CodeGenerator.tsx
│   ├── community/
│   │   ├── VotingSystem.tsx
│   │   ├── CommentSection.tsx
│   │   └── UserProfile.tsx
│   └── showcase/
│       ├── HallOfFame.tsx
│       ├── SuccessStories.tsx
│       └── FeaturedApps.tsx
├── pages/
│   ├── Home.tsx
│   ├── Browse.tsx
│   ├── Generate.tsx
│   ├── Community.tsx
│   └── HallOfFame.tsx
├── hooks/
│   ├── useRedditAPI.ts
│   ├── useMemeAnalysis.ts
│   └── useCommunityData.ts
├── services/
│   ├── redditAPI.ts
│   ├── aiService.ts
│   └── communityService.ts
├── utils/
│   ├── memeParser.ts
│   ├── conceptExtractor.ts
│   └── codeTemplates.ts
└── types/
    ├── meme.ts
    ├── app.ts
    └── community.ts
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Git

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/your-username/memeforge.git
cd memeforge
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment variables:**
```bash
cp .env.example .env.local
```

Edit `.env.local` and configure your settings:
```env
# Application
VITE_APP_NAME="MemeForge"
VITE_APP_VERSION="1.0.0"

# API Configuration
VITE_API_BASE_URL="http://localhost:3000/api"
VITE_REDDIT_API_URL="https://www.reddit.com"

# Rate Limiting
VITE_RATE_LIMIT_REQUESTS_PER_MINUTE=100
VITE_AI_RATE_LIMIT_REQUESTS_PER_MINUTE=30

# Features
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_COMMUNITY_FEATURES=true
VITE_ENABLE_CODE_GENERATION=true
```

4. **Start the development server:**
```bash
npm run dev
```

5. **Open your browser and navigate to `http://localhost:5173`**

### Production Build

```bash
npm run build
npm run preview
```

### Docker Setup (Optional)

```bash
# Build the Docker image
docker build -t memeforge .

# Run the container
docker run -p 5173:5173 memeforge
```

## 🌟 Features

### Core Features
- **Meme Discovery**: Browse trending memes from Reddit with real-time updates
- **AI Analysis**: Extract app concepts from meme themes using advanced algorithms
- **Blueprint Generation**: Visual app architecture creator with detailed specifications
- **Community Voting**: Rate and discuss generated concepts with real-time feedback
- **Hall of Fame**: Showcase successful meme-to-app transformations
- **Code Generation**: Download production-ready starter templates for apps

### Advanced Features
- **Rate Limiting**: Production-ready API rate limiting (100 req/min general, 30 req/min AI)
- **Security**: Input validation, CSRF protection, and content sanitization
- **Real-time Updates**: Live voting, comments, and community interactions
- **Multi-framework Support**: Generate code for React, Next.js, Vue.js, and more
- **TypeScript Support**: Full TypeScript templates with proper typing
- **Responsive Design**: Mobile-first design that works on all devices

## 📖 Usage Examples

### Basic Workflow

1. **Browse Trending Memes**
```typescript
import { useRedditAPI } from '@/hooks/useRedditAPI';

const { memes, getTrendingMemes } = useRedditAPI();
const trendingMemes = getTrendingMemes();
```

2. **Analyze a Meme**
```typescript
import { useMemeAnalysis } from '@/hooks/useMemeAnalysis';

const { analyzeMeme } = useMemeAnalysis();
const analysis = await analyzeMeme.mutateAsync(selectedMeme);
```

3. **Generate App Concept**
```typescript
const { generateAppConcept } = useMemeAnalysis();
const app = await generateAppConcept.mutateAsync({ meme, analysis });
```

4. **Generate Code Template**
```typescript
const { generateCodeTemplate } = useMemeAnalysis();
const template = await generateCodeTemplate.mutateAsync(app);
```

### Community Features

```typescript
import { useCommunityData } from '@/hooks/useCommunityData';

// Submit a vote
const { submitVote } = useCommunityData();
await submitVote.mutateAsync({
  userId: 'user123',
  targetId: 'meme456',
  targetType: 'meme',
  voteType: 'up'
});

// Add a comment
const { addComment } = useCommunityData();
await addComment.mutateAsync({
  userId: 'user123',
  targetId: 'meme456',
  targetType: 'meme',
  content: 'Great meme! This would make an awesome productivity app.'
});
```

## 🔧 API Documentation

### Rate Limits

| Endpoint Type | Limit | Window |
|---------------|-------|--------|
| General API | 100 requests | 1 minute |
| AI Analysis | 30 requests | 1 minute |
| Reddit API | 60 requests | 1 minute |
| User Actions | 10 requests | 1 minute |
| Comments | 5 requests | 1 minute |
| Votes | 20 requests | 1 minute |
| Code Generation | 5 requests | 1 hour |

### Error Handling

```typescript
try {
  const result = await analyzeMeme.mutateAsync(meme);
} catch (error) {
  if (error.message.includes('Rate limit exceeded')) {
    // Handle rate limiting
    console.log('Please wait before trying again');
  } else {
    // Handle other errors
    console.error('Analysis failed:', error.message);
  }
}
```

### Security Features

- **Input Validation**: All user inputs are validated and sanitized
- **CSRF Protection**: Cross-site request forgery protection enabled
- **Content Filtering**: Automatic profanity and malicious content filtering
- **Rate Limiting**: Prevents abuse with configurable rate limits
- **Secure Headers**: Security headers for XSS and clickjacking protection

## 🛠️ Tech Stack

### Frontend
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Shadcn/ui with Radix UI primitives
- **Icons**: Lucide React
- **Animations**: CSS animations and transitions
- **Build Tool**: Vite for fast development and building

### State Management & Data
- **Server State**: TanStack Query (React Query)
- **Client State**: React hooks and context
- **API Integration**: Custom Reddit API wrapper with rate limiting
- **Caching**: Intelligent caching with automatic invalidation

### Development & Quality
- **Language**: TypeScript for type safety
- **Linting**: ESLint with custom rules
- **Formatting**: Prettier for consistent code style
- **Testing**: Vitest and React Testing Library (setup ready)

## 🌍 Environment Variables

Create a `.env.local` file in the root directory:

```env
# Application Configuration
VITE_APP_NAME="MemeForge"
VITE_APP_VERSION="1.0.0"
VITE_APP_DESCRIPTION="Transform memes into reality"

# API Configuration
VITE_API_BASE_URL="http://localhost:3000/api"
VITE_REDDIT_API_URL="https://www.reddit.com"

# Rate Limiting Configuration
VITE_RATE_LIMIT_REQUESTS_PER_MINUTE=100
VITE_AI_RATE_LIMIT_REQUESTS_PER_MINUTE=30
VITE_REDDIT_RATE_LIMIT_REQUESTS_PER_MINUTE=60

# Feature Flags
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_COMMUNITY_FEATURES=true
VITE_ENABLE_CODE_GENERATION=true
VITE_ENABLE_ANALYTICS=false

# Development Settings
VITE_DEBUG_MODE=false
VITE_MOCK_API_RESPONSES=false
```

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect your repository to Vercel**
2. **Set environment variables in Vercel dashboard**
3. **Deploy automatically on push to main branch**

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### Netlify

1. **Build the project:**
```bash
npm run build
```

2. **Deploy the `dist` folder to Netlify**

### Docker Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```bash
# Build and run
docker build -t memeforge .
docker run -p 80:80 memeforge
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Structure

```
src/
├── __tests__/
│   ├── components/
│   ├── hooks/
│   ├── services/
│   └── utils/
└── test-utils/
    ├── setup.ts
    ├── mocks.ts
    └── helpers.ts
```

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch:** `git checkout -b feature/amazing-feature`
3. **Make your changes and add tests**
4. **Commit your changes:** `git commit -m 'Add amazing feature'`
5. **Push to the branch:** `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Use conventional commit messages
- Ensure all tests pass before submitting PR
- Update documentation for new features

### Code Style

```bash
# Format code
npm run format

# Lint code
npm run lint

# Type check
npm run type-check
```

## 📊 Performance

- **Lighthouse Score**: 95+ on all metrics
- **Bundle Size**: < 500KB gzipped
- **First Contentful Paint**: < 1.5s
- **Time to Interactive**: < 3s

## 🔒 Security

- Input validation and sanitization
- CSRF protection
- Rate limiting on all endpoints
- Content Security Policy headers
- XSS protection
- Secure cookie handling

## 📝 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- **Reddit API** for providing meme data
- **OpenAI** for AI analysis inspiration
- **Shadcn/ui** for beautiful UI components
- **Tailwind CSS** for utility-first styling
- **React Community** for amazing ecosystem

## 📞 Support

- **Documentation**: [docs.memeforge.com](https://docs.memeforge.com)
- **Issues**: [GitHub Issues](https://github.com/your-username/memeforge/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/memeforge/discussions)
- **Email**: <EMAIL>

---

Made with ❤️ by the MemeForge community
