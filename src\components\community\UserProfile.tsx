import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  User, 
  Trophy, 
  Star, 
  Calendar, 
  TrendingUp, 
  Code, 
  MessageCircle,
  Download,
  Award,
  Settings,
  Edit,
  Share
} from 'lucide-react';
import { User as UserType, UserStats } from '@/types/community';
import { useCommunityData } from '@/hooks/useCommunityData';

interface UserProfileProps {
  userId: string;
  isOwnProfile?: boolean;
  className?: string;
}

const UserProfile = ({ userId, isOwnProfile = false, className }: UserProfileProps) => {
  const [activeTab, setActiveTab] = useState('overview');
  
  const { useUser, useLeaderboard } = useCommunityData();
  const { data: user, isLoading } = useUser(userId);
  const { data: leaderboard } = useLeaderboard('all-time', 'reputation');

  if (isLoading) {
    return (
      <Card className={`bg-black/30 border-slate-700/50 backdrop-blur-sm ${className}`}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="flex items-center gap-4">
              <div className="h-16 w-16 bg-slate-700 rounded-full"></div>
              <div className="space-y-2">
                <div className="h-4 bg-slate-700 rounded w-32"></div>
                <div className="h-3 bg-slate-700 rounded w-24"></div>
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-16 bg-slate-700 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!user) {
    return (
      <Card className={`bg-black/30 border-slate-700/50 backdrop-blur-sm ${className}`}>
        <CardContent className="p-6 text-center">
          <User className="h-12 w-12 mx-auto mb-3 text-slate-400" />
          <p className="text-slate-400">User not found</p>
        </CardContent>
      </Card>
    );
  }

  const userRank = leaderboard?.entries.findIndex(entry => entry.userId === userId) + 1 || null;
  
  const getNextBadgeProgress = () => {
    const currentRep = user.reputation;
    const nextThresholds = [50, 100, 500, 1000];
    const nextThreshold = nextThresholds.find(threshold => threshold > currentRep);
    
    if (!nextThreshold) return { progress: 100, nextBadge: 'Max Level', remaining: 0 };
    
    const prevThreshold = nextThresholds[nextThresholds.indexOf(nextThreshold) - 1] || 0;
    const progress = ((currentRep - prevThreshold) / (nextThreshold - prevThreshold)) * 100;
    
    const badgeNames = ['Newcomer', 'Creator', 'App Master', 'Meme Legend'];
    const nextBadge = badgeNames[nextThresholds.indexOf(nextThreshold)];
    
    return {
      progress: Math.max(0, Math.min(100, progress)),
      nextBadge,
      remaining: nextThreshold - currentRep
    };
  };

  const badgeProgress = getNextBadgeProgress();

  const formatJoinDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getActivityLevel = (stats: UserStats) => {
    const totalActivity = stats.memesAnalyzed + stats.appsGenerated + stats.communityVotes;
    if (totalActivity >= 100) return { level: 'Very Active', color: 'text-green-400' };
    if (totalActivity >= 50) return { level: 'Active', color: 'text-blue-400' };
    if (totalActivity >= 10) return { level: 'Moderate', color: 'text-yellow-400' };
    return { level: 'New', color: 'text-slate-400' };
  };

  const activity = getActivityLevel(user.stats);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Profile Header */}
      <Card className="bg-gradient-to-r from-purple-900/50 to-blue-900/50 border-purple-500/30 backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16 border-2 border-purple-400">
                <AvatarImage src={user.avatar} alt={user.username} />
                <AvatarFallback className="bg-purple-600 text-white text-xl">
                  {user.username.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              <div>
                <h2 className="text-2xl font-bold text-white mb-1">{user.username}</h2>
                <div className="flex items-center gap-2 mb-2">
                  <Badge 
                    className="text-sm"
                    style={{ 
                      backgroundColor: `${user.badge.color}20`, 
                      color: user.badge.color,
                      borderColor: `${user.badge.color}50`
                    }}
                  >
                    {user.badge.icon} {user.badge.title}
                  </Badge>
                  <Badge className={`text-sm ${activity.color} bg-transparent border-current`}>
                    {activity.level}
                  </Badge>
                  {userRank && userRank <= 10 && (
                    <Badge className="text-sm bg-yellow-600/20 text-yellow-300 border-yellow-500/30">
                      <Trophy className="h-3 w-3 mr-1" />
                      #{userRank}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2 text-slate-400 text-sm">
                  <Calendar className="h-4 w-4" />
                  <span>Joined {formatJoinDate(user.joinDate)}</span>
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              {isOwnProfile ? (
                <Button variant="outline" className="border-purple-500/50 text-purple-300">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Button>
              ) : (
                <>
                  <Button variant="outline" className="border-slate-600 text-slate-300">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Message
                  </Button>
                  <Button variant="outline" className="border-slate-600 text-slate-300">
                    <Share className="h-4 w-4" />
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center p-3 bg-black/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-400">{user.reputation}</div>
              <div className="text-slate-400 text-sm">Reputation</div>
            </div>
            <div className="text-center p-3 bg-black/20 rounded-lg">
              <div className="text-2xl font-bold text-green-400">{user.stats.appsGenerated}</div>
              <div className="text-slate-400 text-sm">Apps Created</div>
            </div>
            <div className="text-center p-3 bg-black/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-400">{user.stats.memesAnalyzed}</div>
              <div className="text-slate-400 text-sm">Memes Analyzed</div>
            </div>
            <div className="text-center p-3 bg-black/20 rounded-lg">
              <div className="text-2xl font-bold text-yellow-400">{user.stats.totalDownloads}</div>
              <div className="text-slate-400 text-sm">Downloads</div>
            </div>
          </div>

          {/* Badge Progress */}
          {badgeProgress.remaining > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-slate-300">Progress to {badgeProgress.nextBadge}</span>
                <span className="text-slate-400">{badgeProgress.remaining} reputation needed</span>
              </div>
              <Progress value={badgeProgress.progress} className="h-2" />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Profile Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 bg-black/30 border border-slate-700">
          <TabsTrigger value="overview" className="data-[state=active]:bg-purple-600/30">
            Overview
          </TabsTrigger>
          <TabsTrigger value="apps" className="data-[state=active]:bg-purple-600/30">
            Apps
          </TabsTrigger>
          <TabsTrigger value="activity" className="data-[state=active]:bg-purple-600/30">
            Activity
          </TabsTrigger>
          <TabsTrigger value="achievements" className="data-[state=active]:bg-purple-600/30">
            Achievements
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid md:grid-cols-2 gap-6">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white">Favorite Tech Stack</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {user.preferences.preferredTechStack.map((tech, index) => (
                    <Badge key={index} className="bg-blue-600/20 text-blue-300 border-blue-500/30">
                      {tech}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white">Favorite Subreddits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {user.preferences.favoriteSubreddits.map((subreddit, index) => (
                    <Badge key={index} className="bg-purple-600/20 text-purple-300 border-purple-500/30">
                      r/{subreddit}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-sm">
                  <Code className="h-4 w-4 text-green-400" />
                  <span className="text-slate-300">Generated 3 new app concepts this week</span>
                  <span className="text-slate-500">2 days ago</span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <TrendingUp className="h-4 w-4 text-blue-400" />
                  <span className="text-slate-300">Analyzed 12 trending memes</span>
                  <span className="text-slate-500">3 days ago</span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <MessageCircle className="h-4 w-4 text-purple-400" />
                  <span className="text-slate-300">Participated in community discussions</span>
                  <span className="text-slate-500">1 week ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Apps Tab */}
        <TabsContent value="apps" className="space-y-4">
          <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Created Apps ({user.stats.appsGenerated})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-slate-400">
                <Code className="h-12 w-12 mx-auto mb-3 opacity-30" />
                <p>App history will be displayed here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-4">
          <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Activity Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-slate-400">
                <TrendingUp className="h-12 w-12 mx-auto mb-3 opacity-30" />
                <p>Activity timeline will be displayed here</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Achievements Tab */}
        <TabsContent value="achievements" className="space-y-4">
          <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Achievements & Badges</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="p-4 bg-slate-800/30 rounded-lg border border-slate-600/50">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="text-2xl">{user.badge.icon}</div>
                    <div>
                      <h4 className="font-semibold text-white">{user.badge.title}</h4>
                      <p className="text-slate-400 text-sm">{user.badge.requirements}</p>
                    </div>
                  </div>
                </div>
                
                {user.stats.successfulApps > 0 && (
                  <div className="p-4 bg-slate-800/30 rounded-lg border border-slate-600/50">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="text-2xl">🚀</div>
                      <div>
                        <h4 className="font-semibold text-white">App Launcher</h4>
                        <p className="text-slate-400 text-sm">Successfully launched {user.stats.successfulApps} apps</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {user.stats.totalDownloads > 1000 && (
                  <div className="p-4 bg-slate-800/30 rounded-lg border border-slate-600/50">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="text-2xl">📈</div>
                      <div>
                        <h4 className="font-semibold text-white">Popular Creator</h4>
                        <p className="text-slate-400 text-sm">Apps downloaded over 1,000 times</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UserProfile;
