import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Meme, MemeAnalysis } from '@/types/meme';
import { GeneratedApp, CodeTemplate } from '@/types/app';

// State interface
interface AppGenerationState {
  // Current workflow step
  currentStep: number;
  
  // Selected meme
  selectedMeme: Meme | null;
  
  // Analysis results
  analysis: MemeAnalysis | null;
  
  // Generated app
  generatedApp: GeneratedApp | null;
  
  // Code template
  codeTemplate: CodeTemplate | null;
  
  // Loading states
  isAnalyzing: boolean;
  isGeneratingApp: boolean;
  isGeneratingCode: boolean;
  
  // Error states
  analysisError: string | null;
  appGenerationError: string | null;
  codeGenerationError: string | null;
  
  // User preferences
  preferences: {
    preferredFramework: string;
    preferredLanguage: string;
    includeTests: boolean;
    includeDocumentation: boolean;
  };
  
  // History
  generationHistory: Array<{
    id: string;
    meme: Meme;
    app: GeneratedApp;
    timestamp: Date;
  }>;
}

// Action types
type AppGenerationAction =
  | { type: 'SET_STEP'; payload: number }
  | { type: 'SELECT_MEME'; payload: Meme }
  | { type: 'START_ANALYSIS' }
  | { type: 'ANALYSIS_SUCCESS'; payload: MemeAnalysis }
  | { type: 'ANALYSIS_ERROR'; payload: string }
  | { type: 'START_APP_GENERATION' }
  | { type: 'APP_GENERATION_SUCCESS'; payload: GeneratedApp }
  | { type: 'APP_GENERATION_ERROR'; payload: string }
  | { type: 'START_CODE_GENERATION' }
  | { type: 'CODE_GENERATION_SUCCESS'; payload: CodeTemplate }
  | { type: 'CODE_GENERATION_ERROR'; payload: string }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<AppGenerationState['preferences']> }
  | { type: 'ADD_TO_HISTORY'; payload: { meme: Meme; app: GeneratedApp } }
  | { type: 'RESET_WORKFLOW' }
  | { type: 'CLEAR_ERRORS' };

// Initial state
const initialState: AppGenerationState = {
  currentStep: 1,
  selectedMeme: null,
  analysis: null,
  generatedApp: null,
  codeTemplate: null,
  isAnalyzing: false,
  isGeneratingApp: false,
  isGeneratingCode: false,
  analysisError: null,
  appGenerationError: null,
  codeGenerationError: null,
  preferences: {
    preferredFramework: 'React',
    preferredLanguage: 'TypeScript',
    includeTests: true,
    includeDocumentation: true,
  },
  generationHistory: [],
};

// Reducer
function appGenerationReducer(state: AppGenerationState, action: AppGenerationAction): AppGenerationState {
  switch (action.type) {
    case 'SET_STEP':
      return { ...state, currentStep: action.payload };
      
    case 'SELECT_MEME':
      return {
        ...state,
        selectedMeme: action.payload,
        currentStep: 2,
        analysis: null,
        generatedApp: null,
        codeTemplate: null,
        analysisError: null,
        appGenerationError: null,
        codeGenerationError: null,
      };
      
    case 'START_ANALYSIS':
      return {
        ...state,
        isAnalyzing: true,
        analysisError: null,
      };
      
    case 'ANALYSIS_SUCCESS':
      return {
        ...state,
        isAnalyzing: false,
        analysis: action.payload,
        currentStep: 3,
        analysisError: null,
      };
      
    case 'ANALYSIS_ERROR':
      return {
        ...state,
        isAnalyzing: false,
        analysisError: action.payload,
      };
      
    case 'START_APP_GENERATION':
      return {
        ...state,
        isGeneratingApp: true,
        appGenerationError: null,
      };
      
    case 'APP_GENERATION_SUCCESS':
      return {
        ...state,
        isGeneratingApp: false,
        generatedApp: action.payload,
        currentStep: 4,
        appGenerationError: null,
      };
      
    case 'APP_GENERATION_ERROR':
      return {
        ...state,
        isGeneratingApp: false,
        appGenerationError: action.payload,
      };
      
    case 'START_CODE_GENERATION':
      return {
        ...state,
        isGeneratingCode: true,
        codeGenerationError: null,
      };
      
    case 'CODE_GENERATION_SUCCESS':
      return {
        ...state,
        isGeneratingCode: false,
        codeTemplate: action.payload,
        currentStep: 5,
        codeGenerationError: null,
      };
      
    case 'CODE_GENERATION_ERROR':
      return {
        ...state,
        isGeneratingCode: false,
        codeGenerationError: action.payload,
      };
      
    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: { ...state.preferences, ...action.payload },
      };
      
    case 'ADD_TO_HISTORY':
      const historyEntry = {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        meme: action.payload.meme,
        app: action.payload.app,
        timestamp: new Date(),
      };
      return {
        ...state,
        generationHistory: [historyEntry, ...state.generationHistory.slice(0, 9)], // Keep last 10
      };
      
    case 'RESET_WORKFLOW':
      return {
        ...state,
        currentStep: 1,
        selectedMeme: null,
        analysis: null,
        generatedApp: null,
        codeTemplate: null,
        isAnalyzing: false,
        isGeneratingApp: false,
        isGeneratingCode: false,
        analysisError: null,
        appGenerationError: null,
        codeGenerationError: null,
      };
      
    case 'CLEAR_ERRORS':
      return {
        ...state,
        analysisError: null,
        appGenerationError: null,
        codeGenerationError: null,
      };
      
    default:
      return state;
  }
}

// Context
interface AppGenerationContextType {
  state: AppGenerationState;
  dispatch: React.Dispatch<AppGenerationAction>;
  
  // Helper functions
  selectMeme: (meme: Meme) => void;
  startAnalysis: () => void;
  setAnalysisResult: (analysis: MemeAnalysis) => void;
  setAnalysisError: (error: string) => void;
  startAppGeneration: () => void;
  setAppGenerationResult: (app: GeneratedApp) => void;
  setAppGenerationError: (error: string) => void;
  startCodeGeneration: () => void;
  setCodeGenerationResult: (template: CodeTemplate) => void;
  setCodeGenerationError: (error: string) => void;
  updatePreferences: (preferences: Partial<AppGenerationState['preferences']>) => void;
  addToHistory: (meme: Meme, app: GeneratedApp) => void;
  resetWorkflow: () => void;
  clearErrors: () => void;
  goToStep: (step: number) => void;
  
  // Computed properties
  canProceedToNextStep: () => boolean;
  getCurrentStepTitle: () => string;
  getCurrentStepDescription: () => string;
  getProgressPercentage: () => number;
  hasErrors: () => boolean;
  isProcessing: () => boolean;
}

const AppGenerationContext = createContext<AppGenerationContextType | undefined>(undefined);

// Provider component
interface AppGenerationProviderProps {
  children: ReactNode;
}

export const AppGenerationProvider: React.FC<AppGenerationProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appGenerationReducer, initialState);

  // Helper functions
  const selectMeme = (meme: Meme) => {
    dispatch({ type: 'SELECT_MEME', payload: meme });
  };

  const startAnalysis = () => {
    dispatch({ type: 'START_ANALYSIS' });
  };

  const setAnalysisResult = (analysis: MemeAnalysis) => {
    dispatch({ type: 'ANALYSIS_SUCCESS', payload: analysis });
  };

  const setAnalysisError = (error: string) => {
    dispatch({ type: 'ANALYSIS_ERROR', payload: error });
  };

  const startAppGeneration = () => {
    dispatch({ type: 'START_APP_GENERATION' });
  };

  const setAppGenerationResult = (app: GeneratedApp) => {
    dispatch({ type: 'APP_GENERATION_SUCCESS', payload: app });
    if (state.selectedMeme) {
      addToHistory(state.selectedMeme, app);
    }
  };

  const setAppGenerationError = (error: string) => {
    dispatch({ type: 'APP_GENERATION_ERROR', payload: error });
  };

  const startCodeGeneration = () => {
    dispatch({ type: 'START_CODE_GENERATION' });
  };

  const setCodeGenerationResult = (template: CodeTemplate) => {
    dispatch({ type: 'CODE_GENERATION_SUCCESS', payload: template });
  };

  const setCodeGenerationError = (error: string) => {
    dispatch({ type: 'CODE_GENERATION_ERROR', payload: error });
  };

  const updatePreferences = (preferences: Partial<AppGenerationState['preferences']>) => {
    dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
  };

  const addToHistory = (meme: Meme, app: GeneratedApp) => {
    dispatch({ type: 'ADD_TO_HISTORY', payload: { meme, app } });
  };

  const resetWorkflow = () => {
    dispatch({ type: 'RESET_WORKFLOW' });
  };

  const clearErrors = () => {
    dispatch({ type: 'CLEAR_ERRORS' });
  };

  const goToStep = (step: number) => {
    dispatch({ type: 'SET_STEP', payload: step });
  };

  // Computed properties
  const canProceedToNextStep = (): boolean => {
    switch (state.currentStep) {
      case 1: return !!state.selectedMeme;
      case 2: return !!state.analysis;
      case 3: return !!state.generatedApp;
      case 4: return !!state.codeTemplate;
      default: return false;
    }
  };

  const getCurrentStepTitle = (): string => {
    const titles = {
      1: 'Select Meme',
      2: 'AI Analysis',
      3: 'Generate App',
      4: 'Get Code',
      5: 'Complete',
    };
    return titles[state.currentStep as keyof typeof titles] || 'Unknown Step';
  };

  const getCurrentStepDescription = (): string => {
    const descriptions = {
      1: 'Choose a meme to transform into an app',
      2: 'AI analyzes the meme for themes and concepts',
      3: 'Generate a complete app blueprint',
      4: 'Download production-ready code',
      5: 'Your app is ready to build!',
    };
    return descriptions[state.currentStep as keyof typeof descriptions] || '';
  };

  const getProgressPercentage = (): number => {
    const baseProgress = ((state.currentStep - 1) / 4) * 100;
    
    // Add sub-progress for current step
    if (state.isAnalyzing) return baseProgress + 10;
    if (state.isGeneratingApp) return baseProgress + 15;
    if (state.isGeneratingCode) return baseProgress + 20;
    
    return baseProgress;
  };

  const hasErrors = (): boolean => {
    return !!(state.analysisError || state.appGenerationError || state.codeGenerationError);
  };

  const isProcessing = (): boolean => {
    return state.isAnalyzing || state.isGeneratingApp || state.isGeneratingCode;
  };

  const contextValue: AppGenerationContextType = {
    state,
    dispatch,
    selectMeme,
    startAnalysis,
    setAnalysisResult,
    setAnalysisError,
    startAppGeneration,
    setAppGenerationResult,
    setAppGenerationError,
    startCodeGeneration,
    setCodeGenerationResult,
    setCodeGenerationError,
    updatePreferences,
    addToHistory,
    resetWorkflow,
    clearErrors,
    goToStep,
    canProceedToNextStep,
    getCurrentStepTitle,
    getCurrentStepDescription,
    getProgressPercentage,
    hasErrors,
    isProcessing,
  };

  return (
    <AppGenerationContext.Provider value={contextValue}>
      {children}
    </AppGenerationContext.Provider>
  );
};

// Hook to use the context
export const useAppGeneration = (): AppGenerationContextType => {
  const context = useContext(AppGenerationContext);
  if (context === undefined) {
    throw new Error('useAppGeneration must be used within an AppGenerationProvider');
  }
  return context;
};

export default AppGenerationContext;
