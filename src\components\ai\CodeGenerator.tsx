import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { 
  Code, 
  Download, 
  Copy, 
  FileText, 
  Package, 
  Terminal,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  ExternalLink,
  Zap
} from 'lucide-react';
import { GeneratedApp, CodeTemplate } from '@/types/app';
import { useMemeAnalysis } from '@/hooks/useMemeAnalysis';

interface CodeGeneratorProps {
  app: GeneratedApp;
  onCodeGenerated?: (template: CodeTemplate) => void;
  className?: string;
}

const CodeGenerator = ({ app, onCodeGenerated, className }: CodeGeneratorProps) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [copiedFile, setCopiedFile] = useState<string | null>(null);
  
  const { 
    generateCodeTemplate, 
    useCodeTemplate,
    isProcessing,
    getError 
  } = useMemeAnalysis();

  const { data: codeTemplate, isLoading: templateLoading } = useCodeTemplate(
    generateCodeTemplate.data?.id || ''
  );

  const handleGenerateCode = () => {
    generateCodeTemplate.mutate(app, {
      onSuccess: (template) => {
        onCodeGenerated?.(template);
      }
    });
  };

  const copyToClipboard = async (content: string, fileName?: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedFile(fileName || 'content');
      setTimeout(() => setCopiedFile(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const downloadFile = (content: string, fileName: string) => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const downloadAllFiles = () => {
    if (!codeTemplate) return;
    
    // Create a zip-like structure by downloading individual files
    codeTemplate.files.forEach(file => {
      downloadFile(file.content, file.path.split('/').pop() || 'file.txt');
    });
  };

  const getLanguageFromExtension = (path: string) => {
    const ext = path.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'tsx':
      case 'jsx': return 'javascript';
      case 'ts': return 'typescript';
      case 'js': return 'javascript';
      case 'css': return 'css';
      case 'html': return 'html';
      case 'json': return 'json';
      case 'md': return 'markdown';
      default: return 'text';
    }
  };

  const error = getError();
  const template = codeTemplate || generateCodeTemplate.data;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-green-900/50 to-blue-900/50 border-green-500/30 backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Code className="h-8 w-8 text-green-400" />
              <div>
                <CardTitle className="text-white text-2xl">Code Generator</CardTitle>
                <CardDescription className="text-slate-300">
                  Generate production-ready code for {app.name}
                </CardDescription>
              </div>
            </div>
            <div className="flex gap-2">
              {!template && (
                <Button
                  onClick={handleGenerateCode}
                  disabled={isProcessing()}
                  className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  {generateCodeTemplate.isPending ? 'Generating...' : 'Generate Code'}
                </Button>
              )}
              {template && (
                <Button
                  onClick={downloadAllFiles}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download All
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Progress Indicator */}
      {generateCodeTemplate.isPending && (
        <Card className="bg-black/30 border-blue-500/50 backdrop-blur-sm">
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-blue-300 font-medium">Generating code templates...</span>
                <RefreshCw className="h-5 w-5 text-blue-400 animate-spin" />
              </div>
              <Progress value={75} className="h-2" />
              <p className="text-slate-400 text-sm">
                Creating files, setting up dependencies, and generating documentation...
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Card className="bg-red-900/30 border-red-500/50 backdrop-blur-sm">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-300">
              <AlertCircle className="h-5 w-5" />
              <span>{error.message}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Code Template Display */}
      {template && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-black/30 border border-slate-700">
            <TabsTrigger value="overview" className="data-[state=active]:bg-green-600/30">
              Overview
            </TabsTrigger>
            <TabsTrigger value="files" className="data-[state=active]:bg-green-600/30">
              Files
            </TabsTrigger>
            <TabsTrigger value="dependencies" className="data-[state=active]:bg-green-600/30">
              Dependencies
            </TabsTrigger>
            <TabsTrigger value="setup" className="data-[state=active]:bg-green-600/30">
              Setup
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  Code Template Generated
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-green-400 mb-3">Template Info</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-slate-400">Name:</span>
                        <span className="text-white">{template.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Language:</span>
                        <Badge className="bg-blue-600/20 text-blue-300">
                          {template.language}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Framework:</span>
                        <Badge className="bg-purple-600/20 text-purple-300">
                          {template.framework}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">Files:</span>
                        <span className="text-white">{template.files.length}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-blue-400 mb-3">Quick Stats</h4>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                        <div className="text-xl font-bold text-green-400">{template.files.length}</div>
                        <div className="text-slate-400 text-xs">Files</div>
                      </div>
                      <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                        <div className="text-xl font-bold text-blue-400">{template.dependencies.length}</div>
                        <div className="text-slate-400 text-xs">Dependencies</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-purple-400 mb-2">Description</h4>
                  <p className="text-slate-300">{template.description}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Files Tab */}
          <TabsContent value="files" className="space-y-4">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Generated Files ({template.files.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-4">
                    {template.files.map((file, index) => (
                      <Card key={index} className="bg-slate-800/30 border-slate-600/50">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <FileText className="h-4 w-4 text-blue-400" />
                              <code className="text-white font-mono text-sm">{file.path}</code>
                              <Badge className="text-xs bg-slate-600/50 text-slate-300">
                                {getLanguageFromExtension(file.path)}
                              </Badge>
                            </div>
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => copyToClipboard(file.content, file.path)}
                                className="h-6 w-6 p-0"
                              >
                                {copiedFile === file.path ? (
                                  <CheckCircle className="h-3 w-3 text-green-400" />
                                ) : (
                                  <Copy className="h-3 w-3" />
                                )}
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => downloadFile(file.content, file.path.split('/').pop() || 'file.txt')}
                                className="h-6 w-6 p-0"
                              >
                                <Download className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          
                          <p className="text-slate-400 text-sm mb-3">{file.description}</p>
                          
                          <ScrollArea className="h-32">
                            <pre className="text-xs text-slate-300 font-mono bg-slate-900/50 p-3 rounded overflow-x-auto">
                              <code>{file.content}</code>
                            </pre>
                          </ScrollArea>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Dependencies Tab */}
          <TabsContent value="dependencies" className="space-y-4">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Dependencies ({template.dependencies.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Package installation command:</span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(`npm install ${template.dependencies.join(' ')}`)}
                      className="border-slate-600 text-slate-300"
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy
                    </Button>
                  </div>
                  
                  <pre className="bg-slate-900/50 p-3 rounded text-sm text-green-400 font-mono overflow-x-auto">
                    <code>npm install {template.dependencies.join(' ')}</code>
                  </pre>

                  <div className="grid gap-2 mt-4">
                    <h4 className="font-semibold text-blue-400">Individual Dependencies:</h4>
                    <div className="grid md:grid-cols-2 gap-2">
                      {template.dependencies.map((dep, index) => (
                        <div key={index} className="flex items-center justify-between bg-slate-800/30 p-2 rounded">
                          <code className="text-slate-300 font-mono text-sm">{dep}</code>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyToClipboard(dep)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Setup Tab */}
          <TabsContent value="setup" className="space-y-4">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Terminal className="h-5 w-5" />
                  Setup Instructions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-300">Copy all setup commands:</span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(template.setupInstructions.join('\n'))}
                      className="border-slate-600 text-slate-300"
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy All
                    </Button>
                  </div>

                  <div className="space-y-3">
                    {template.setupInstructions.map((instruction, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                          {index + 1}
                        </div>
                        <div className="flex-1 bg-slate-800/30 p-3 rounded">
                          <p className="text-slate-300">{instruction}</p>
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(instruction)}
                          className="h-6 w-6 p-0 mt-2"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-5 w-5 text-green-400" />
                      <span className="font-semibold text-green-400">Ready to Deploy!</span>
                    </div>
                    <p className="text-slate-300 text-sm">
                      Your {app.name} starter template is ready. Follow the setup instructions above to get started.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default CodeGenerator;
