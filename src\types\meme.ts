
export interface Meme {
  id: string;
  title: string;
  imageUrl: string;
  subreddit: string;
  upvotes: number;
  comments: number;
  trending: boolean;
  appPotential: number;
  author: string;
  createdAt: Date;
  tags?: string[];
  nsfw?: boolean;
}

export interface MemeAnalysis {
  memeId: string;
  themes: string[];
  concepts: AppConcept[];
  techStack: string[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
  marketPotential: number;
  analysisDate: Date;
}

export interface AppConcept {
  id: string;
  name: string;
  description: string;
  features: string[];
  targetAudience: string;
  monetizationStrategy: string[];
}

export interface RedditPostData {
  id: string;
  title: string;
  url: string;
  thumbnail: string;
  subreddit: string;
  ups: number;
  num_comments: number;
  created_utc: number;
  author: string;
  selftext: string;
  over_18: boolean;
  preview?: {
    images: Array<{
      source: {
        url: string;
        width: number;
        height: number;
      };
    }>;
  };
}

export interface SubredditConfig {
  name: string;
  displayName: string;
  category: 'tech' | 'memes' | 'general';
  weight: number; // For app potential calculation
}

export const SUBREDDIT_CONFIGS: SubredditConfig[] = [
  { name: 'ProgrammerHumor', displayName: 'Programmer Humor', category: 'tech', weight: 1.0 },
  { name: 'webdev', displayName: 'Web Development', category: 'tech', weight: 0.9 },
  { name: 'javascript', displayName: 'JavaScript', category: 'tech', weight: 0.8 },
  { name: 'reactjs', displayName: 'React', category: 'tech', weight: 0.8 },
  { name: 'css', displayName: 'CSS', category: 'tech', weight: 0.7 },
  { name: 'dankmemes', displayName: 'Dank Memes', category: 'memes', weight: 0.6 },
  { name: 'memes', displayName: 'Memes', category: 'memes', weight: 0.5 },
];
