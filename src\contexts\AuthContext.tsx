import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';

// User interface
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  reputation: number;
  badge: {
    title: string;
    icon: string;
    color: string;
    requirements: string;
  };
  joinDate: Date;
  subscription: {
    plan: 'free' | 'pro' | 'enterprise';
    expiresAt?: Date;
    features: string[];
  };
  stats: {
    memesAnalyzed: number;
    appsGenerated: number;
    codeDownloads: number;
    communityVotes: number;
    successfulApps: number;
    totalDownloads: number;
  };
  preferences: {
    preferredTechStack: string[];
    favoriteSubreddits: string[];
    notifications: {
      email: boolean;
      push: boolean;
      community: boolean;
    };
  };
}

// Auth state interface
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  sessionToken: string | null;
}

// Action types
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SET_LOADING'; payload: boolean };

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  sessionToken: null,
};

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
      
    case 'AUTH_SUCCESS':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        user: action.payload.user,
        sessionToken: action.payload.token,
        error: null,
      };
      
    case 'AUTH_ERROR':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        sessionToken: null,
        error: action.payload,
      };
      
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        sessionToken: null,
        error: null,
      };
      
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
      
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
      
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
      
    default:
      return state;
  }
}

// Context interface
interface AuthContextType {
  state: AuthState;
  dispatch: React.Dispatch<AuthAction>;
  
  // Auth methods
  login: (email: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  
  // Utility methods
  hasFeature: (feature: string) => boolean;
  canPerformAction: (action: string) => boolean;
  getRemainingUsage: (resource: string) => number;
  isSubscriptionActive: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock API functions (replace with real API calls)
const mockAPI = {
  async login(email: string, password: string): Promise<{ user: User; token: string }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock validation
    if (email === '<EMAIL>' && password === 'demo123') {
      const user: User = {
        id: 'user_demo',
        username: 'DemoUser',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        reputation: 1250,
        badge: {
          title: 'Creator',
          icon: '⭐',
          color: '#10B981',
          requirements: 'Generate 5 successful apps'
        },
        joinDate: new Date('2024-01-15'),
        subscription: {
          plan: 'pro',
          expiresAt: new Date('2024-12-31'),
          features: ['unlimited_analysis', 'code_generation', 'priority_support']
        },
        stats: {
          memesAnalyzed: 45,
          appsGenerated: 12,
          codeDownloads: 8,
          communityVotes: 156,
          successfulApps: 3,
          totalDownloads: 2500
        },
        preferences: {
          preferredTechStack: ['React', 'TypeScript', 'Next.js'],
          favoriteSubreddits: ['ProgrammerHumor', 'webdev', 'reactjs'],
          notifications: {
            email: true,
            push: true,
            community: true
          }
        }
      };
      
      return {
        user,
        token: 'mock_jwt_token_' + Date.now()
      };
    }
    
    throw new Error('Invalid email or password');
  },
  
  async register(username: string, email: string, password: string): Promise<{ user: User; token: string }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Mock validation
    if (username.length < 3) {
      throw new Error('Username must be at least 3 characters');
    }
    
    if (!email.includes('@')) {
      throw new Error('Invalid email address');
    }
    
    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters');
    }
    
    const user: User = {
      id: 'user_' + Date.now(),
      username,
      email,
      reputation: 0,
      badge: {
        title: 'Newcomer',
        icon: '🌟',
        color: '#6B7280',
        requirements: 'Welcome to MemeForge!'
      },
      joinDate: new Date(),
      subscription: {
        plan: 'free',
        features: ['basic_analysis', 'limited_generation']
      },
      stats: {
        memesAnalyzed: 0,
        appsGenerated: 0,
        codeDownloads: 0,
        communityVotes: 0,
        successfulApps: 0,
        totalDownloads: 0
      },
      preferences: {
        preferredTechStack: ['React'],
        favoriteSubreddits: ['ProgrammerHumor'],
        notifications: {
          email: true,
          push: false,
          community: true
        }
      }
    };
    
    return {
      user,
      token: 'mock_jwt_token_' + Date.now()
    };
  }
};

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load user from localStorage on mount
  useEffect(() => {
    const savedUser = localStorage.getItem('memeforge_user');
    const savedToken = localStorage.getItem('memeforge_token');
    
    if (savedUser && savedToken) {
      try {
        const user = JSON.parse(savedUser);
        dispatch({ type: 'AUTH_SUCCESS', payload: { user, token: savedToken } });
      } catch (error) {
        // Clear invalid data
        localStorage.removeItem('memeforge_user');
        localStorage.removeItem('memeforge_token');
      }
    }
  }, []);

  // Auth methods
  const login = async (email: string, password: string): Promise<void> => {
    dispatch({ type: 'AUTH_START' });
    
    try {
      const { user, token } = await mockAPI.login(email, password);
      
      // Save to localStorage
      localStorage.setItem('memeforge_user', JSON.stringify(user));
      localStorage.setItem('memeforge_token', token);
      
      dispatch({ type: 'AUTH_SUCCESS', payload: { user, token } });
    } catch (error) {
      dispatch({ type: 'AUTH_ERROR', payload: error instanceof Error ? error.message : 'Login failed' });
    }
  };

  const register = async (username: string, email: string, password: string): Promise<void> => {
    dispatch({ type: 'AUTH_START' });
    
    try {
      const { user, token } = await mockAPI.register(username, email, password);
      
      // Save to localStorage
      localStorage.setItem('memeforge_user', JSON.stringify(user));
      localStorage.setItem('memeforge_token', token);
      
      dispatch({ type: 'AUTH_SUCCESS', payload: { user, token } });
    } catch (error) {
      dispatch({ type: 'AUTH_ERROR', payload: error instanceof Error ? error.message : 'Registration failed' });
    }
  };

  const logout = (): void => {
    // Clear localStorage
    localStorage.removeItem('memeforge_user');
    localStorage.removeItem('memeforge_token');
    
    dispatch({ type: 'LOGOUT' });
  };

  const updateProfile = async (updates: Partial<User>): Promise<void> => {
    if (!state.user) return;
    
    dispatch({ type: 'SET_LOADING', payload: true });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedUser = { ...state.user, ...updates };
      
      // Update localStorage
      localStorage.setItem('memeforge_user', JSON.stringify(updatedUser));
      
      dispatch({ type: 'UPDATE_USER', payload: updates });
    } catch (error) {
      dispatch({ type: 'AUTH_ERROR', payload: 'Failed to update profile' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Utility methods
  const hasFeature = (feature: string): boolean => {
    return state.user?.subscription.features.includes(feature) || false;
  };

  const canPerformAction = (action: string): boolean => {
    if (!state.isAuthenticated) return false;
    
    const limits = {
      free: {
        daily_analysis: 5,
        monthly_generation: 3,
        code_downloads: 1
      },
      pro: {
        daily_analysis: 100,
        monthly_generation: 50,
        code_downloads: 25
      },
      enterprise: {
        daily_analysis: -1, // unlimited
        monthly_generation: -1,
        code_downloads: -1
      }
    };
    
    const userLimits = limits[state.user?.subscription.plan || 'free'];
    return userLimits[action as keyof typeof userLimits] === -1 || true; // Simplified for demo
  };

  const getRemainingUsage = (resource: string): number => {
    // Mock implementation - in real app, this would check actual usage
    const limits = {
      free: { daily_analysis: 5, monthly_generation: 3 },
      pro: { daily_analysis: 100, monthly_generation: 50 },
      enterprise: { daily_analysis: -1, monthly_generation: -1 }
    };
    
    const userLimits = limits[state.user?.subscription.plan || 'free'];
    const limit = userLimits[resource as keyof typeof userLimits];
    
    if (limit === -1) return -1; // unlimited
    
    // Mock current usage
    const mockUsage = { daily_analysis: 2, monthly_generation: 1 };
    return Math.max(0, limit - (mockUsage[resource as keyof typeof mockUsage] || 0));
  };

  const isSubscriptionActive = (): boolean => {
    if (!state.user?.subscription.expiresAt) return true; // Free plan or no expiration
    return new Date() < state.user.subscription.expiresAt;
  };

  const contextValue: AuthContextType = {
    state,
    dispatch,
    login,
    register,
    logout,
    updateProfile,
    hasFeature,
    canPerformAction,
    getRemainingUsage,
    isSubscriptionActive,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use the context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
