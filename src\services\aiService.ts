import { Meme, MemeAnalysis, AppConcept } from '@/types/meme';
import { GeneratedApp, AppBlueprint, ComponentSpec, DataModel, APIEndpoint, UserFlowStep, CodeTemplate } from '@/types/app';

// Rate limiting for AI service
const AI_RATE_LIMIT = {
  requests: 30, // requests per minute
  window: 60 * 1000, // 1 minute in milliseconds
};

class AIRateLimiter {
  private requests: number[] = [];

  canMakeRequest(): boolean {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < AI_RATE_LIMIT.window);
    
    if (this.requests.length >= AI_RATE_LIMIT.requests) {
      return false;
    }
    
    this.requests.push(now);
    return true;
  }

  getTimeUntilNextRequest(): number {
    if (this.requests.length < AI_RATE_LIMIT.requests) {
      return 0;
    }
    
    const oldestRequest = Math.min(...this.requests);
    return AI_RATE_LIMIT.window - (Date.now() - oldestRequest);
  }
}

class AIService {
  private rateLimiter = new AIRateLimiter();
  private analysisCache = new Map<string, { analysis: MemeAnalysis; timestamp: number }>();
  private cacheTimeout = 30 * 60 * 1000; // 30 minutes

  async analyzeMeme(meme: Meme): Promise<MemeAnalysis> {
    // Check rate limit
    if (!this.rateLimiter.canMakeRequest()) {
      const waitTime = this.rateLimiter.getTimeUntilNextRequest();
      throw new Error(`AI service rate limit exceeded. Try again in ${Math.ceil(waitTime / 1000)} seconds.`);
    }

    // Check cache
    const cached = this.analysisCache.get(meme.id);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.analysis;
    }

    try {
      // Simulate AI analysis with realistic processing time
      await this.simulateProcessingDelay();
      
      const analysis = await this.performMemeAnalysis(meme);
      
      // Cache the result
      this.analysisCache.set(meme.id, { analysis, timestamp: Date.now() });
      
      return analysis;
    } catch (error) {
      console.error('AI analysis failed:', error);
      throw new Error('Failed to analyze meme. Please try again later.');
    }
  }

  async generateAppConcept(meme: Meme, analysis: MemeAnalysis): Promise<GeneratedApp> {
    // Check rate limit
    if (!this.rateLimiter.canMakeRequest()) {
      const waitTime = this.rateLimiter.getTimeUntilNextRequest();
      throw new Error(`AI service rate limit exceeded. Try again in ${Math.ceil(waitTime / 1000)} seconds.`);
    }

    try {
      await this.simulateProcessingDelay();
      
      const concept = analysis.concepts[0]; // Use the first concept
      const blueprint = await this.generateBlueprint(concept, analysis);
      
      const generatedApp: GeneratedApp = {
        id: `app_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: concept.name,
        description: concept.description,
        originalMemeId: meme.id,
        originalMemeTitle: meme.title,
        creator: 'AI Generator',
        createdAt: new Date(),
        rating: 0,
        downloads: 0,
        imageUrl: meme.imageUrl,
        tags: [...analysis.themes, ...analysis.techStack],
        featured: false,
        status: 'concept',
        techStack: analysis.techStack,
        features: concept.features.map((feature, index) => ({
          id: `feature_${index}`,
          name: feature,
          description: `Implementation of ${feature}`,
          priority: index < 3 ? 'high' : index < 6 ? 'medium' : 'low',
          implemented: false,
          estimatedHours: Math.floor(Math.random() * 20) + 5,
        })),
        blueprint,
      };

      return generatedApp;
    } catch (error) {
      console.error('App concept generation failed:', error);
      throw new Error('Failed to generate app concept. Please try again later.');
    }
  }

  async generateCodeTemplate(app: GeneratedApp): Promise<CodeTemplate> {
    // Check rate limit
    if (!this.rateLimiter.canMakeRequest()) {
      const waitTime = this.rateLimiter.getTimeUntilNextRequest();
      throw new Error(`AI service rate limit exceeded. Try again in ${Math.ceil(waitTime / 1000)} seconds.`);
    }

    try {
      await this.simulateProcessingDelay();
      
      const template: CodeTemplate = {
        id: `template_${app.id}`,
        name: `${app.name} Starter Template`,
        description: `Complete starter template for ${app.name}`,
        language: 'TypeScript',
        framework: this.selectFramework(app.techStack),
        files: await this.generateTemplateFiles(app),
        dependencies: this.generateDependencies(app.techStack),
        setupInstructions: this.generateSetupInstructions(app),
      };

      return template;
    } catch (error) {
      console.error('Code template generation failed:', error);
      throw new Error('Failed to generate code template. Please try again later.');
    }
  }

  private async performMemeAnalysis(meme: Meme): Promise<MemeAnalysis> {
    // Extract themes based on meme content and subreddit
    const themes = this.extractThemes(meme);
    
    // Generate app concepts based on themes
    const concepts = await this.generateConcepts(meme, themes);
    
    // Determine tech stack based on themes and concepts
    const techStack = this.determineTechStack(themes, concepts);
    
    // Calculate difficulty and time estimates
    const difficulty = this.calculateDifficulty(concepts, techStack);
    const estimatedTime = this.estimateTime(difficulty, concepts);
    
    return {
      memeId: meme.id,
      themes,
      concepts,
      techStack,
      difficulty,
      estimatedTime,
      marketPotential: meme.appPotential,
      analysisDate: new Date(),
    };
  }

  private extractThemes(meme: Meme): string[] {
    const themes: string[] = [];
    const title = meme.title.toLowerCase();
    const subreddit = meme.subreddit.toLowerCase();
    
    // Theme mapping based on common meme patterns
    const themeMap = {
      'productivity': ['bug', 'fix', 'debug', 'work', 'deadline', 'task', 'todo'],
      'developer-tools': ['code', 'programming', 'git', 'ide', 'terminal', 'stack'],
      'social': ['team', 'meeting', 'communication', 'chat', 'collaboration'],
      'gaming': ['game', 'play', 'level', 'achievement', 'score', 'competition'],
      'education': ['learn', 'tutorial', 'course', 'skill', 'knowledge', 'practice'],
      'automation': ['script', 'bot', 'automatic', 'workflow', 'process'],
      'ui-ux': ['design', 'interface', 'user', 'experience', 'layout', 'css'],
      'data': ['database', 'api', 'json', 'data', 'analytics', 'chart'],
    };

    Object.entries(themeMap).forEach(([theme, keywords]) => {
      if (keywords.some(keyword => title.includes(keyword) || subreddit.includes(keyword))) {
        themes.push(theme);
      }
    });

    // Add subreddit-specific themes
    if (subreddit.includes('programmer') || subreddit.includes('webdev')) {
      themes.push('developer-tools');
    }
    
    return themes.length > 0 ? themes : ['general', 'productivity'];
  }

  private async generateConcepts(meme: Meme, themes: string[]): Promise<AppConcept[]> {
    const concepts: AppConcept[] = [];
    
    // Generate 2-3 concepts based on themes
    for (let i = 0; i < Math.min(3, themes.length + 1); i++) {
      const concept = this.createConceptFromTheme(meme, themes[i] || themes[0], i);
      concepts.push(concept);
    }
    
    return concepts;
  }

  private createConceptFromTheme(meme: Meme, theme: string, index: number): AppConcept {
    const conceptTemplates = {
      'productivity': {
        names: ['TaskMaster Pro', 'ProductivityHub', 'FocusFlow'],
        features: ['Task Management', 'Time Tracking', 'Goal Setting', 'Progress Analytics', 'Team Collaboration'],
        audience: 'Professionals and students looking to improve productivity',
        monetization: ['Freemium Model', 'Premium Subscriptions', 'Team Plans'],
      },
      'developer-tools': {
        names: ['DevHelper', 'CodeAssist', 'BugTracker Pro'],
        features: ['Code Analysis', 'Bug Tracking', 'Performance Monitoring', 'Team Integration', 'Automated Testing'],
        audience: 'Software developers and development teams',
        monetization: ['SaaS Subscription', 'Enterprise Licenses', 'API Usage Fees'],
      },
      'social': {
        names: ['ConnectHub', 'TeamSync', 'CommunityBuilder'],
        features: ['Real-time Chat', 'Video Calls', 'File Sharing', 'Project Boards', 'User Profiles'],
        audience: 'Teams and communities seeking better communication',
        monetization: ['Freemium Model', 'Premium Features', 'Custom Branding'],
      },
      'gaming': {
        names: ['GameTracker', 'AchievementHub', 'CompeteNow'],
        features: ['Score Tracking', 'Leaderboards', 'Achievements', 'Social Features', 'Tournament Mode'],
        audience: 'Gamers and competitive communities',
        monetization: ['In-app Purchases', 'Premium Memberships', 'Tournament Fees'],
      },
    };

    const template = conceptTemplates[theme as keyof typeof conceptTemplates] || conceptTemplates['productivity'];
    
    return {
      id: `concept_${index}`,
      name: template.names[index % template.names.length],
      description: `A ${theme}-focused application inspired by "${meme.title}"`,
      features: template.features,
      targetAudience: template.audience,
      monetizationStrategy: template.monetization,
    };
  }

  private determineTechStack(themes: string[], concepts: AppConcept[]): string[] {
    const baseStack = ['React', 'TypeScript', 'Node.js'];
    
    // Add stack based on themes
    if (themes.includes('data')) {
      baseStack.push('PostgreSQL', 'Prisma');
    }
    
    if (themes.includes('social')) {
      baseStack.push('Socket.io', 'Redis');
    }
    
    if (themes.includes('ui-ux')) {
      baseStack.push('Tailwind CSS', 'Framer Motion');
    }
    
    // Add authentication for most apps
    baseStack.push('NextAuth.js');
    
    return [...new Set(baseStack)];
  }

  private calculateDifficulty(concepts: AppConcept[], techStack: string[]): 'Easy' | 'Medium' | 'Hard' {
    const complexityScore = concepts.reduce((score, concept) => score + concept.features.length, 0) + techStack.length;
    
    if (complexityScore < 10) return 'Easy';
    if (complexityScore < 20) return 'Medium';
    return 'Hard';
  }

  private estimateTime(difficulty: 'Easy' | 'Medium' | 'Hard', concepts: AppConcept[]): string {
    const baseHours = {
      'Easy': 40,
      'Medium': 80,
      'Hard': 160,
    };
    
    const totalFeatures = concepts.reduce((count, concept) => count + concept.features.length, 0);
    const estimatedHours = baseHours[difficulty] + (totalFeatures * 8);
    
    if (estimatedHours < 80) return `${estimatedHours} hours (1-2 weeks)`;
    if (estimatedHours < 160) return `${estimatedHours} hours (3-4 weeks)`;
    return `${estimatedHours} hours (2-3 months)`;
  }

  private async generateBlueprint(concept: AppConcept, analysis: MemeAnalysis): Promise<AppBlueprint> {
    return {
      architecture: 'Full-stack web application with React frontend and Node.js backend',
      components: this.generateComponents(concept),
      dataModels: this.generateDataModels(concept),
      apiEndpoints: this.generateAPIEndpoints(concept),
      userFlow: this.generateUserFlow(concept),
    };
  }

  private generateComponents(concept: AppConcept): ComponentSpec[] {
    return [
      {
        name: 'App',
        type: 'component',
        description: 'Main application component',
        props: [],
        dependencies: ['React', 'Router'],
      },
      {
        name: 'Dashboard',
        type: 'page',
        description: 'Main dashboard page',
        props: ['user'],
        dependencies: ['React', 'UI Components'],
      },
      {
        name: 'FeatureList',
        type: 'component',
        description: 'List of main features',
        props: ['features', 'onSelect'],
        dependencies: ['React'],
      },
    ];
  }

  private generateDataModels(concept: AppConcept): DataModel[] {
    return [
      {
        name: 'User',
        fields: [
          { name: 'id', type: 'string', required: true, description: 'Unique user identifier' },
          { name: 'email', type: 'string', required: true, description: 'User email address' },
          { name: 'name', type: 'string', required: true, description: 'User display name' },
        ],
        relationships: ['Profile', 'Sessions'],
      },
    ];
  }

  private generateAPIEndpoints(concept: AppConcept): APIEndpoint[] {
    return [
      {
        path: '/api/users',
        method: 'GET',
        description: 'Get user list',
        responseBody: 'User[]',
      },
      {
        path: '/api/users',
        method: 'POST',
        description: 'Create new user',
        requestBody: 'CreateUserRequest',
        responseBody: 'User',
      },
    ];
  }

  private generateUserFlow(concept: AppConcept): UserFlowStep[] {
    return [
      {
        step: 1,
        title: 'Landing',
        description: 'User visits the application',
        screen: 'LandingPage',
        actions: ['View features', 'Sign up', 'Sign in'],
      },
      {
        step: 2,
        title: 'Authentication',
        description: 'User creates account or signs in',
        screen: 'AuthPage',
        actions: ['Enter credentials', 'Verify email', 'Complete profile'],
      },
    ];
  }

  private selectFramework(techStack: string[]): string {
    if (techStack.includes('React')) return 'React';
    if (techStack.includes('Vue')) return 'Vue.js';
    if (techStack.includes('Angular')) return 'Angular';
    return 'React';
  }

  private async generateTemplateFiles(app: GeneratedApp): Promise<any[]> {
    // This would generate actual code files - simplified for now
    return [
      {
        path: 'src/App.tsx',
        content: `// ${app.name} - Generated from meme: "${app.originalMemeTitle}"\n\nfunction App() {\n  return <div>Hello ${app.name}!</div>;\n}\n\nexport default App;`,
        description: 'Main application component',
      },
    ];
  }

  private generateDependencies(techStack: string[]): string[] {
    const depMap: Record<string, string> = {
      'React': 'react@^18.0.0',
      'TypeScript': 'typescript@^5.0.0',
      'Node.js': '@types/node@^20.0.0',
      'Tailwind CSS': 'tailwindcss@^3.0.0',
    };

    return techStack.map(tech => depMap[tech] || tech.toLowerCase()).filter(Boolean);
  }

  private generateSetupInstructions(app: GeneratedApp): string[] {
    return [
      'Clone the repository',
      'Run `npm install` to install dependencies',
      'Copy `.env.example` to `.env` and configure environment variables',
      'Run `npm run dev` to start the development server',
      'Open http://localhost:3000 in your browser',
    ];
  }

  private async simulateProcessingDelay(): Promise<void> {
    // Simulate realistic AI processing time
    const delay = Math.random() * 2000 + 1000; // 1-3 seconds
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  // Get rate limit status
  getRateLimitStatus() {
    return {
      requestsRemaining: Math.max(0, AI_RATE_LIMIT.requests - this.rateLimiter['requests'].length),
      resetTime: this.rateLimiter.getTimeUntilNextRequest(),
    };
  }

  // Clear analysis cache
  clearCache() {
    this.analysisCache.clear();
  }
}

export const aiService = new AIService();
export default aiService;
