import { Meme, AppConcept } from '@/types/meme';

/**
 * Utility for extracting app concepts from memes using pattern matching and heuristics
 */

export class ConceptExtractor {
  // Pattern definitions for different types of app concepts
  private static readonly CONCEPT_PATTERNS = {
    productivity: {
      keywords: ['task', 'todo', 'organize', 'schedule', 'deadline', 'workflow', 'efficiency'],
      themes: ['time management', 'organization', 'planning', 'automation'],
      appTypes: ['Task Manager', 'Productivity Suite', 'Time Tracker', 'Workflow Optimizer']
    },
    
    developer_tools: {
      keywords: ['code', 'debug', 'git', 'deploy', 'test', 'api', 'documentation'],
      themes: ['development', 'debugging', 'version control', 'testing', 'deployment'],
      appTypes: ['Code Editor', 'Debug Assistant', 'API Tester', 'Documentation Generator']
    },
    
    social: {
      keywords: ['chat', 'message', 'team', 'collaborate', 'share', 'community'],
      themes: ['communication', 'collaboration', 'social networking', 'team work'],
      appTypes: ['Chat App', 'Team Collaboration', 'Social Platform', 'Community Hub']
    },
    
    entertainment: {
      keywords: ['game', 'fun', 'play', 'entertainment', 'music', 'video'],
      themes: ['gaming', 'entertainment', 'media', 'leisure'],
      appTypes: ['Game', 'Media Player', 'Entertainment Hub', 'Interactive Experience']
    },
    
    education: {
      keywords: ['learn', 'study', 'tutorial', 'course', 'knowledge', 'skill'],
      themes: ['learning', 'education', 'training', 'skill development'],
      appTypes: ['Learning Platform', 'Tutorial App', 'Skill Tracker', 'Educational Game']
    },
    
    utility: {
      keywords: ['tool', 'utility', 'helper', 'calculator', 'converter', 'generator'],
      themes: ['utilities', 'tools', 'calculations', 'conversions'],
      appTypes: ['Utility Tool', 'Calculator', 'Converter', 'Generator']
    },
    
    health_fitness: {
      keywords: ['health', 'fitness', 'exercise', 'diet', 'wellness', 'medical'],
      themes: ['health tracking', 'fitness', 'wellness', 'medical'],
      appTypes: ['Health Tracker', 'Fitness App', 'Wellness Platform', 'Medical Assistant']
    },
    
    finance: {
      keywords: ['money', 'budget', 'expense', 'investment', 'finance', 'payment'],
      themes: ['financial management', 'budgeting', 'investments', 'payments'],
      appTypes: ['Budget Tracker', 'Expense Manager', 'Investment App', 'Payment System']
    }
  };

  /**
   * Extract app concepts from a meme
   */
  static extractConcepts(meme: Meme): AppConcept[] {
    const concepts: AppConcept[] = [];
    const text = `${meme.title} ${meme.subreddit}`.toLowerCase();
    
    // Analyze text for patterns
    const detectedCategories = this.detectCategories(text);
    
    // Generate concepts for each detected category
    detectedCategories.forEach((category, index) => {
      const concept = this.generateConceptForCategory(meme, category, index);
      if (concept) {
        concepts.push(concept);
      }
    });
    
    // If no specific categories detected, generate a generic concept
    if (concepts.length === 0) {
      const genericConcept = this.generateGenericConcept(meme);
      concepts.push(genericConcept);
    }
    
    return concepts.slice(0, 3); // Limit to 3 concepts
  }

  /**
   * Detect categories based on text analysis
   */
  private static detectCategories(text: string): string[] {
    const categories: { category: string; score: number }[] = [];
    
    Object.entries(this.CONCEPT_PATTERNS).forEach(([category, pattern]) => {
      let score = 0;
      
      // Check for keyword matches
      pattern.keywords.forEach(keyword => {
        if (text.includes(keyword)) {
          score += 2;
        }
      });
      
      // Check for theme-related words
      pattern.themes.forEach(theme => {
        const themeWords = theme.split(' ');
        themeWords.forEach(word => {
          if (text.includes(word)) {
            score += 1;
          }
        });
      });
      
      if (score > 0) {
        categories.push({ category, score });
      }
    });
    
    // Sort by score and return top categories
    return categories
      .sort((a, b) => b.score - a.score)
      .slice(0, 2)
      .map(item => item.category);
  }

  /**
   * Generate a concept for a specific category
   */
  private static generateConceptForCategory(
    meme: Meme, 
    category: string, 
    index: number
  ): AppConcept | null {
    const pattern = this.CONCEPT_PATTERNS[category as keyof typeof this.CONCEPT_PATTERNS];
    if (!pattern) return null;

    const appType = pattern.appTypes[index % pattern.appTypes.length];
    const conceptName = this.generateConceptName(meme, appType);
    
    return {
      id: `concept_${category}_${index}`,
      name: conceptName,
      description: this.generateDescription(meme, category, appType),
      features: this.generateFeatures(category, meme),
      targetAudience: this.generateTargetAudience(category),
      monetizationStrategy: this.generateMonetizationStrategy(category)
    };
  }

  /**
   * Generate a generic concept when no specific category is detected
   */
  private static generateGenericConcept(meme: Meme): AppConcept {
    return {
      id: 'concept_generic_0',
      name: this.generateGenericName(meme),
      description: `An innovative application inspired by the viral meme "${meme.title}". This app transforms the humor and relatability of the meme into a practical digital solution.`,
      features: [
        'User-friendly interface',
        'Social sharing capabilities',
        'Personalization options',
        'Community features',
        'Mobile-responsive design'
      ],
      targetAudience: 'General users who appreciate internet culture and viral content',
      monetizationStrategy: ['Freemium model', 'In-app advertising', 'Premium features']
    };
  }

  /**
   * Generate concept name based on meme and app type
   */
  private static generateConceptName(meme: Meme, appType: string): string {
    const memeWords = meme.title.split(' ').filter(word => word.length > 3);
    const relevantWord = memeWords[0] || 'Meme';
    
    const nameTemplates = [
      `${relevantWord} ${appType}`,
      `${appType} Pro`,
      `Smart ${appType}`,
      `${relevantWord}ify`,
      `The ${relevantWord} App`
    ];
    
    return nameTemplates[Math.floor(Math.random() * nameTemplates.length)];
  }

  /**
   * Generate generic name for concepts
   */
  private static generateGenericName(meme: Meme): string {
    const memeWords = meme.title.split(' ').filter(word => word.length > 3);
    const relevantWord = memeWords[0] || 'Viral';
    
    const nameTemplates = [
      `${relevantWord} Hub`,
      `${relevantWord} Central`,
      `The ${relevantWord} App`,
      `${relevantWord}ify`,
      `${relevantWord} Connect`
    ];
    
    return nameTemplates[Math.floor(Math.random() * nameTemplates.length)];
  }

  /**
   * Generate description for concept
   */
  private static generateDescription(meme: Meme, category: string, appType: string): string {
    const categoryDescriptions = {
      productivity: `A powerful ${appType.toLowerCase()} that helps users manage their tasks and boost productivity, inspired by the relatable struggles shown in "${meme.title}".`,
      developer_tools: `An essential ${appType.toLowerCase()} designed for developers who understand the pain points highlighted in "${meme.title}".`,
      social: `A ${appType.toLowerCase()} that brings people together around shared experiences, just like the viral moment captured in "${meme.title}".`,
      entertainment: `An engaging ${appType.toLowerCase()} that turns the humor of "${meme.title}" into an interactive entertainment experience.`,
      education: `A comprehensive ${appType.toLowerCase()} that makes learning fun and relatable, drawing inspiration from "${meme.title}".`,
      utility: `A practical ${appType.toLowerCase()} that solves everyday problems, motivated by the common experience depicted in "${meme.title}".`,
      health_fitness: `A motivating ${appType.toLowerCase()} that helps users achieve their health goals while keeping things light-hearted, inspired by "${meme.title}".`,
      finance: `A user-friendly ${appType.toLowerCase()} that makes financial management less stressful, addressing the concerns shown in "${meme.title}".`
    };
    
    return categoryDescriptions[category as keyof typeof categoryDescriptions] || 
           `An innovative ${appType.toLowerCase()} inspired by the viral meme "${meme.title}".`;
  }

  /**
   * Generate features based on category
   */
  private static generateFeatures(category: string, meme: Meme): string[] {
    const categoryFeatures = {
      productivity: [
        'Task management and scheduling',
        'Progress tracking and analytics',
        'Team collaboration tools',
        'Automated reminders',
        'Goal setting and achievement',
        'Time tracking integration',
        'Priority management'
      ],
      developer_tools: [
        'Code analysis and suggestions',
        'Bug tracking and reporting',
        'Integration with popular IDEs',
        'Automated testing tools',
        'Documentation generation',
        'Performance monitoring',
        'Collaboration features'
      ],
      social: [
        'Real-time messaging',
        'Group creation and management',
        'Content sharing and discovery',
        'User profiles and customization',
        'Community moderation tools',
        'Event planning and coordination',
        'Social media integration'
      ],
      entertainment: [
        'Interactive gameplay elements',
        'Leaderboards and achievements',
        'Social sharing capabilities',
        'Customizable user experience',
        'Regular content updates',
        'Multiplayer functionality',
        'Offline mode support'
      ],
      education: [
        'Interactive learning modules',
        'Progress tracking and certificates',
        'Personalized learning paths',
        'Community discussion forums',
        'Expert instructor content',
        'Mobile learning support',
        'Assessment and quizzing tools'
      ],
      utility: [
        'Easy-to-use interface',
        'Offline functionality',
        'Data export and import',
        'Customizable settings',
        'Quick access shortcuts',
        'Integration with other apps',
        'Regular feature updates'
      ],
      health_fitness: [
        'Activity tracking and monitoring',
        'Goal setting and progress tracking',
        'Social challenges and competitions',
        'Personalized recommendations',
        'Integration with wearable devices',
        'Nutrition and meal planning',
        'Health insights and analytics'
      ],
      finance: [
        'Expense tracking and categorization',
        'Budget planning and monitoring',
        'Investment portfolio management',
        'Bill reminders and automation',
        'Financial goal setting',
        'Security and encryption',
        'Reporting and analytics'
      ]
    };
    
    const features = categoryFeatures[category as keyof typeof categoryFeatures] || [
      'User-friendly interface',
      'Cross-platform compatibility',
      'Data synchronization',
      'Customizable settings',
      'Regular updates'
    ];
    
    return features.slice(0, 5); // Return top 5 features
  }

  /**
   * Generate target audience based on category
   */
  private static generateTargetAudience(category: string): string {
    const audiences = {
      productivity: 'Professionals, students, and anyone looking to improve their productivity and time management',
      developer_tools: 'Software developers, programmers, and technical teams working on code projects',
      social: 'Social media users, communities, and groups looking for better communication tools',
      entertainment: 'Gamers, entertainment enthusiasts, and users seeking fun interactive experiences',
      education: 'Students, educators, professionals seeking skill development, and lifelong learners',
      utility: 'General users needing practical tools for everyday tasks and problem-solving',
      health_fitness: 'Health-conscious individuals, fitness enthusiasts, and people pursuing wellness goals',
      finance: 'Individuals and families looking to manage their finances, budgets, and investments'
    };
    
    return audiences[category as keyof typeof audiences] || 
           'General users interested in innovative digital solutions';
  }

  /**
   * Generate monetization strategy based on category
   */
  private static generateMonetizationStrategy(category: string): string[] {
    const strategies = {
      productivity: ['Freemium model with premium features', 'Team and enterprise subscriptions', 'Integration marketplace'],
      developer_tools: ['SaaS subscription model', 'Enterprise licenses', 'API usage fees', 'Professional support'],
      social: ['Freemium with premium features', 'In-app advertising', 'Virtual goods and gifts', 'Premium memberships'],
      entertainment: ['In-app purchases', 'Premium content subscriptions', 'Advertising revenue', 'Merchandise sales'],
      education: ['Course and content subscriptions', 'Certification fees', 'Corporate training licenses', 'Freemium model'],
      utility: ['One-time purchase', 'Premium feature upgrades', 'Ad-supported free version', 'Professional licenses'],
      health_fitness: ['Subscription for premium features', 'Personal coaching services', 'Partner integrations', 'Corporate wellness'],
      finance: ['Freemium with premium tools', 'Transaction fees', 'Investment advisory services', 'Premium analytics']
    };
    
    return strategies[category as keyof typeof strategies] || 
           ['Freemium model', 'Premium subscriptions', 'In-app advertising'];
  }

  /**
   * Analyze meme sentiment and adjust concepts accordingly
   */
  static analyzeSentiment(meme: Meme): 'positive' | 'negative' | 'neutral' {
    const title = meme.title.toLowerCase();
    
    const positiveWords = ['love', 'great', 'awesome', 'amazing', 'perfect', 'best', 'good', 'happy', 'success'];
    const negativeWords = ['hate', 'terrible', 'awful', 'worst', 'bad', 'frustrated', 'annoying', 'problem', 'fail'];
    
    let positiveScore = 0;
    let negativeScore = 0;
    
    positiveWords.forEach(word => {
      if (title.includes(word)) positiveScore++;
    });
    
    negativeWords.forEach(word => {
      if (title.includes(word)) negativeScore++;
    });
    
    if (positiveScore > negativeScore) return 'positive';
    if (negativeScore > positiveScore) return 'negative';
    return 'neutral';
  }

  /**
   * Get concept recommendations based on meme analysis
   */
  static getRecommendations(meme: Meme): string[] {
    const recommendations: string[] = [];
    const sentiment = this.analyzeSentiment(meme);
    
    // Sentiment-based recommendations
    if (sentiment === 'negative') {
      recommendations.push('Consider focusing on problem-solving features');
      recommendations.push('Emphasize user pain point resolution');
    } else if (sentiment === 'positive') {
      recommendations.push('Leverage the positive energy in your app design');
      recommendations.push('Focus on community and sharing features');
    }
    
    // Engagement-based recommendations
    if (meme.upvotes > 10000) {
      recommendations.push('High engagement suggests broad appeal - consider B2C approach');
    }
    
    if (meme.comments > 200) {
      recommendations.push('High comment count indicates discussion potential - add community features');
    }
    
    // Subreddit-specific recommendations
    if (meme.subreddit.toLowerCase().includes('programmer')) {
      recommendations.push('Target developer audience with technical features');
    }
    
    if (meme.subreddit.toLowerCase().includes('meme')) {
      recommendations.push('Incorporate humor and viral elements into the app');
    }
    
    return recommendations;
  }
}

export default ConceptExtractor;
