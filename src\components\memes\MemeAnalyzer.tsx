
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Brain, Lightbulb, Code, Rocket, Download, Share2 } from 'lucide-react';

interface AnalysisResult {
  themes: string[];
  concepts: string[];
  techStack: string[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
  marketPotential: number;
}

interface MemeAnalyzerProps {
  meme: {
    id: string;
    title: string;
    imageUrl: string;
    subreddit: string;
  };
  onClose: () => void;
}

const MemeAnalyzer = ({ meme, onClose }: MemeAnalyzerProps) => {
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [analysis, setAnalysis] = useState<AnalysisResult | null>(null);

  const startAnalysis = () => {
    setAnalysisProgress(0);
    const interval = setInterval(() => {
      setAnalysisProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setAnalysisComplete(true);
          // Mock analysis results
          setAnalysis({
            themes: ['Problem Solving', 'Developer Experience', 'Automation', 'Productivity'],
            concepts: [
              'Bug Tracking Dashboard',
              'AI Code Assistant',
              'Developer Mood Tracker',
              'Automated Testing Suite'
            ],
            techStack: ['React', 'Node.js', 'AI/ML APIs', 'Database'],
            difficulty: 'Medium',
            estimatedTime: '2-4 weeks',
            marketPotential: 78
          });
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 200);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-400 border-green-500/50';
      case 'Medium': return 'text-yellow-400 border-yellow-500/50';
      case 'Hard': return 'text-red-400 border-red-500/50';
      default: return 'text-gray-400 border-gray-500/50';
    }
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="bg-black/90 border-purple-500/50 backdrop-blur-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Brain className="h-8 w-8 text-purple-400" />
              <div>
                <CardTitle className="text-white text-2xl">AI Meme Analysis</CardTitle>
                <CardDescription className="text-slate-300">
                  "{meme.title}" from r/{meme.subreddit}
                </CardDescription>
              </div>
            </div>
            <Button onClick={onClose} variant="ghost" className="text-slate-400 hover:text-white">
              ✕
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <img 
                src={meme.imageUrl} 
                alt={meme.title}
                className="w-full h-64 object-cover rounded-lg border border-purple-500/30"
              />
            </div>
            
            <div className="space-y-4">
              {!analysisComplete ? (
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-white">Analysis Progress</h3>
                  <Progress value={analysisProgress} className="h-3" />
                  <p className="text-slate-400">
                    {analysisProgress < 30 ? 'Scanning meme patterns...' :
                     analysisProgress < 60 ? 'Extracting core themes...' :
                     analysisProgress < 90 ? 'Generating app concepts...' :
                     'Finalizing analysis...'}
                  </p>
                  {analysisProgress === 0 && (
                    <Button 
                      onClick={startAnalysis}
                      className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      Start Analysis
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-green-400">✓ Analysis Complete</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-slate-400">Difficulty</p>
                      <Badge className={`${getDifficultyColor(analysis!.difficulty)} bg-transparent`}>
                        {analysis!.difficulty}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-slate-400">Est. Time</p>
                      <p className="text-white font-semibold">{analysis!.estimatedTime}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-slate-400 mb-2">Market Potential</p>
                    <Progress value={analysis!.marketPotential} className="h-2" />
                    <p className="text-sm text-slate-400 mt-1">{analysis!.marketPotential}%</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {analysisComplete && analysis && (
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="bg-black/40 border-purple-500/30">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Lightbulb className="h-5 w-5 text-yellow-400" />
                    Detected Themes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {analysis.themes.map((theme, index) => (
                      <Badge key={index} className="bg-purple-600/20 text-purple-300 border-purple-500/30">
                        {theme}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-black/40 border-green-500/30">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Rocket className="h-5 w-5 text-green-400" />
                    App Concepts
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {analysis.concepts.map((concept, index) => (
                      <li key={index} className="text-slate-300 flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                        {concept}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-black/40 border-blue-500/30 md:col-span-2">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Code className="h-5 w-5 text-blue-400" />
                    Recommended Tech Stack
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {analysis.techStack.map((tech, index) => (
                      <Badge key={index} className="bg-blue-600/20 text-blue-300 border-blue-500/30">
                        {tech}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {analysisComplete && (
            <div className="flex flex-wrap gap-3 justify-center pt-4">
              <Button className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
                <Rocket className="h-4 w-4 mr-2" />
                Generate Full Blueprint
              </Button>
              <Button variant="outline" className="border-purple-500/50 text-purple-300 hover:bg-purple-900/30">
                <Download className="h-4 w-4 mr-2" />
                Download Code Templates
              </Button>
              <Button variant="outline" className="border-blue-500/50 text-blue-300 hover:bg-blue-900/30">
                <Share2 className="h-4 w-4 mr-2" />
                Share Analysis
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MemeAnalyzer;
