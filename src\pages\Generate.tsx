import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAppGeneration } from '@/contexts/AppGenerationContext';
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  Brain,
  Zap,
  Code,
  FileText,
  Lightbulb,
  ArrowRight,
  CheckCircle,
  Download,
  Share,
  AlertCircle
} from 'lucide-react';
import { Meme } from '@/types/meme';
import { GeneratedApp } from '@/types/app';
import { useRedditAPI } from '@/hooks/useRedditAPI';
import { useMemeAnalysis } from '@/hooks/useMemeAnalysis';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import MemeCard from '@/components/memes/MemeCard';
import ConceptGenerator from '@/components/ai/ConceptGenerator';
import AppBlueprint from '@/components/ai/AppBlueprint';
import CodeGenerator from '@/components/ai/CodeGenerator';

const Generate = () => {
  const {
    state,
    selectMeme,
    startAnalysis,
    setAnalysisResult,
    setAnalysisError,
    startAppGeneration,
    setAppGenerationResult,
    setAppGenerationError,
    startCodeGeneration,
    setCodeGenerationResult,
    setCodeGenerationError,
    resetWorkflow,
    getCurrentStepTitle,
    getCurrentStepDescription,
    getProgressPercentage,
    isProcessing,
    hasErrors
  } = useAppGeneration();

  const [activeTab, setActiveTab] = useState('concept');

  const { getTrendingMemes, getHighPotentialMemes } = useRedditAPI();
  const { analyzeMeme, generateAppConcept, generateCodeTemplate } = useMemeAnalysis();

  const trendingMemes = getTrendingMemes();
  const highPotentialMemes = getHighPotentialMemes();

  const steps = [
    { id: 1, title: 'Select Meme', description: 'Choose a meme to transform', icon: <Lightbulb className="h-5 w-5" /> },
    { id: 2, title: 'AI Analysis', description: 'Extract themes and concepts', icon: <Brain className="h-5 w-5" /> },
    { id: 3, title: 'Generate App', description: 'Create app blueprint', icon: <Zap className="h-5 w-5" /> },
    { id: 4, title: 'Get Code', description: 'Download starter template', icon: <Code className="h-5 w-5" /> },
  ];

  const handleMemeSelect = async (meme: Meme) => {
    selectMeme(meme);

    // Start analysis automatically
    startAnalysis();
    try {
      const analysis = await analyzeMeme.mutateAsync(meme);
      setAnalysisResult(analysis);
    } catch (error) {
      setAnalysisError(error instanceof Error ? error.message : 'Analysis failed');
    }
  };

  const handleGenerateApp = async () => {
    if (!state.selectedMeme || !state.analysis) return;

    startAppGeneration();
    try {
      const app = await generateAppConcept.mutateAsync({
        meme: state.selectedMeme,
        analysis: state.analysis
      });
      setAppGenerationResult(app);
    } catch (error) {
      setAppGenerationError(error instanceof Error ? error.message : 'App generation failed');
    }
  };

  const handleGenerateCode = async () => {
    if (!state.generatedApp) return;

    startCodeGeneration();
    try {
      const template = await generateCodeTemplate.mutateAsync(state.generatedApp);
      setCodeGenerationResult(template);
    } catch (error) {
      setCodeGenerationError(error instanceof Error ? error.message : 'Code generation failed');
    }
  };

  const getStepStatus = (stepId: number) => {
    if (stepId < state.currentStep) return 'complete';
    if (stepId === state.currentStep) return 'active';
    return 'pending';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-green-400 via-blue-400 to-purple-600 bg-clip-text text-transparent">
            Generate Apps
          </h1>
          <p className="text-xl text-slate-300 mb-6 max-w-2xl mx-auto">
            Transform any meme into a functional app concept with AI-powered analysis and code generation
          </p>
        </div>

        {/* Progress Steps */}
        <Card className="bg-black/30 border-purple-800/30 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Generation Process</h3>
              <Badge className="bg-blue-600/20 text-blue-300 border-blue-500/30">
                Step {state.currentStep} of {steps.length}
              </Badge>
            </div>

            <Progress value={getProgressPercentage()} className="h-2 mb-6" />

            {hasErrors() && (
              <div className="mb-4 p-3 bg-red-900/30 border border-red-500/50 rounded-lg">
                <div className="flex items-center gap-2 text-red-300">
                  <AlertCircle className="h-4 w-4" />
                  <span>
                    {state.analysisError || state.appGenerationError || state.codeGenerationError}
                  </span>
                </div>
              </div>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {steps.map((step, index) => {
                const status = getStepStatus(step.id);
                return (
                  <div key={step.id} className="flex items-center gap-3">
                    <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                      status === 'complete' ? 'bg-green-600 text-white' :
                      status === 'active' ? 'bg-blue-600 text-white' :
                      'bg-slate-700 text-slate-400'
                    }`}>
                      {status === 'complete' ? <CheckCircle className="h-5 w-5" /> : step.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className={`font-medium ${
                        status === 'active' ? 'text-blue-300' : 
                        status === 'complete' ? 'text-green-300' : 
                        'text-slate-400'
                      }`}>
                        {step.title}
                      </h4>
                      <p className="text-xs text-slate-500">{step.description}</p>
                    </div>
                    {index < steps.length - 1 && (
                      <ArrowRight className="h-4 w-4 text-slate-600 hidden md:block" />
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        {state.currentStep === 1 && (
          <div className="space-y-8">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white">Choose Your Meme</CardTitle>
                <CardDescription className="text-slate-300">
                  Select a meme to transform into an app concept. Higher potential memes generate better results.
                </CardDescription>
              </CardHeader>
            </Card>

            <Tabs defaultValue="trending" className="w-full">
              <TabsList className="grid w-full grid-cols-2 bg-black/30 border border-slate-700">
                <TabsTrigger value="trending" className="data-[state=active]:bg-purple-600/30">
                  Trending Memes
                </TabsTrigger>
                <TabsTrigger value="high-potential" className="data-[state=active]:bg-purple-600/30">
                  High Potential
                </TabsTrigger>
              </TabsList>

              <TabsContent value="trending" className="space-y-4">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {trendingMemes.slice(0, 6).map((meme) => (
                    <MemeCard 
                      key={meme.id} 
                      meme={meme} 
                      onAnalyze={() => handleMemeSelect(meme)}
                    />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="high-potential" className="space-y-4">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {highPotentialMemes.slice(0, 6).map((meme) => (
                    <MemeCard 
                      key={meme.id} 
                      meme={meme} 
                      onAnalyze={() => handleMemeSelect(meme)}
                    />
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {state.currentStep === 2 && state.selectedMeme && (
          <div className="space-y-6">
            <Card className="bg-black/30 border-blue-500/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Brain className="h-6 w-6 text-blue-400" />
                  AI Analysis in Progress
                </CardTitle>
                <CardDescription className="text-slate-300">
                  Analyzing "{state.selectedMeme.title}" for app potential and themes
                </CardDescription>
              </CardHeader>
              <CardContent>
                {state.isAnalyzing ? (
                  <div className="text-center py-8">
                    <div className="animate-spin h-12 w-12 border-4 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
                    <p className="text-slate-300">Extracting themes and concepts...</p>
                  </div>
                ) : state.analysis ? (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 text-green-400">
                      <CheckCircle className="h-5 w-5" />
                      <span>Analysis complete!</span>
                    </div>
                    <Button
                      onClick={handleGenerateApp}
                      className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      Generate App Concept
                    </Button>
                  </div>
                ) : null}
              </CardContent>
            </Card>
          </div>
        )}

        {state.currentStep >= 3 && state.generatedApp && (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-black/30 border border-slate-700 mb-8">
              <TabsTrigger value="concept" className="data-[state=active]:bg-green-600/30">
                <Brain className="h-4 w-4 mr-2" />
                App Concept
              </TabsTrigger>
              <TabsTrigger value="blueprint" className="data-[state=active]:bg-green-600/30">
                <FileText className="h-4 w-4 mr-2" />
                Blueprint
              </TabsTrigger>
              <TabsTrigger value="code" className="data-[state=active]:bg-green-600/30">
                <Code className="h-4 w-4 mr-2" />
                Code Generator
              </TabsTrigger>
            </TabsList>

            <TabsContent value="concept">
              <Card className="bg-gradient-to-r from-green-900/50 to-blue-900/50 border-green-500/30 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-white text-2xl">
                        {state.generatedApp?.name}
                      </CardTitle>
                      <CardDescription className="text-slate-300">
                        Generated from: "{state.selectedMeme?.title}"
                      </CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={handleGenerateCode}
                        disabled={state.isGeneratingCode}
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                      >
                        <Code className="h-4 w-4 mr-2" />
                        {state.isGeneratingCode ? 'Generating...' : 'Generate Code'}
                      </Button>
                      <Button variant="outline" className="border-green-500/50 text-green-300">
                        <Share className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <p className="text-slate-300 text-lg">
                    {state.generatedApp?.description}
                  </p>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-green-400 mb-3">Key Features</h4>
                      <ul className="space-y-2">
                        {state.generatedApp?.features.slice(0, 5).map((feature, index) => (
                          <li key={index} className="flex items-center gap-2 text-slate-300">
                            <CheckCircle className="h-4 w-4 text-green-400" />
                            {feature.name}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-blue-400 mb-3">Tech Stack</h4>
                      <div className="flex flex-wrap gap-2">
                        {state.generatedApp?.techStack.map((tech, index) => (
                          <Badge key={index} className="bg-blue-600/20 text-blue-300 border-blue-500/30">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="blueprint">
              {state.generatedApp && (
                <AppBlueprint
                  app={state.generatedApp}
                  onDownloadBlueprint={() => console.log('Download blueprint')}
                  onViewCode={() => setActiveTab('code')}
                />
              )}
            </TabsContent>

            <TabsContent value="code">
              {state.generatedApp && (
                <CodeGenerator
                  app={state.generatedApp}
                  onCodeGenerated={(template) => setCodeGenerationResult(template)}
                />
              )}
            </TabsContent>
          </Tabs>
        )}

        {state.currentStep >= 4 && state.codeTemplate && (
          <Card className="bg-gradient-to-r from-green-900/50 to-blue-900/50 border-green-500/30 backdrop-blur-sm">
            <CardHeader>
              <div className="flex items-center gap-3">
                <CheckCircle className="h-8 w-8 text-green-400" />
                <div>
                  <CardTitle className="text-white text-2xl">Generation Complete!</CardTitle>
                  <CardDescription className="text-slate-300">
                    Your app concept and code template are ready
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-black/20 rounded-lg">
                  <Brain className="h-8 w-8 mx-auto mb-2 text-purple-400" />
                  <h4 className="font-semibold text-white mb-1">Concept Ready</h4>
                  <p className="text-slate-400 text-sm">AI analysis complete</p>
                </div>
                <div className="text-center p-4 bg-black/20 rounded-lg">
                  <FileText className="h-8 w-8 mx-auto mb-2 text-blue-400" />
                  <h4 className="font-semibold text-white mb-1">Blueprint Created</h4>
                  <p className="text-slate-400 text-sm">Architecture defined</p>
                </div>
                <div className="text-center p-4 bg-black/20 rounded-lg">
                  <Code className="h-8 w-8 mx-auto mb-2 text-green-400" />
                  <h4 className="font-semibold text-white mb-1">Code Generated</h4>
                  <p className="text-slate-400 text-sm">Ready to download</p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
                  <Download className="h-4 w-4 mr-2" />
                  Download Code Template
                </Button>
                <Button variant="outline" className="border-purple-500/50 text-purple-300">
                  <Share className="h-4 w-4 mr-2" />
                  Share Your Creation
                </Button>
                <Button
                  variant="outline"
                  onClick={resetWorkflow}
                  className="border-slate-600 text-slate-300"
                >
                  Generate Another
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <Footer />
    </div>
  );
};

export default Generate;
