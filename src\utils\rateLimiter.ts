/**
 * Rate limiting utilities for API endpoints and user actions
 */

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (identifier: string) => string; // Custom key generator
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = {
      keyGenerator: (id) => id,
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      ...config
    };
  }

  /**
   * Check if a request is allowed and update the rate limit
   */
  checkLimit(identifier: string): RateLimitResult {
    const key = this.config.keyGenerator!(identifier);
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    // Get existing requests for this key
    let userRequests = this.requests.get(key) || [];

    // Remove requests outside the current window
    userRequests = userRequests.filter(timestamp => timestamp > windowStart);

    // Check if limit is exceeded
    const allowed = userRequests.length < this.config.maxRequests;
    
    if (allowed) {
      // Add current request
      userRequests.push(now);
      this.requests.set(key, userRequests);
    }

    const remaining = Math.max(0, this.config.maxRequests - userRequests.length);
    const resetTime = now + this.config.windowMs;
    const retryAfter = allowed ? undefined : this.getRetryAfter(userRequests, windowStart);

    return {
      allowed,
      remaining,
      resetTime,
      retryAfter
    };
  }

  /**
   * Calculate retry after time in seconds
   */
  private getRetryAfter(requests: number[], windowStart: number): number {
    if (requests.length === 0) return 0;
    
    const oldestRequest = Math.min(...requests);
    const timeUntilReset = (oldestRequest + this.config.windowMs) - Date.now();
    return Math.ceil(timeUntilReset / 1000);
  }

  /**
   * Reset rate limit for a specific identifier
   */
  reset(identifier: string): void {
    const key = this.config.keyGenerator!(identifier);
    this.requests.delete(key);
  }

  /**
   * Get current status for an identifier
   */
  getStatus(identifier: string): RateLimitResult {
    const key = this.config.keyGenerator!(identifier);
    const now = Date.now();
    const windowStart = now - this.config.windowMs;

    let userRequests = this.requests.get(key) || [];
    userRequests = userRequests.filter(timestamp => timestamp > windowStart);

    const remaining = Math.max(0, this.config.maxRequests - userRequests.length);
    const resetTime = now + this.config.windowMs;

    return {
      allowed: userRequests.length < this.config.maxRequests,
      remaining,
      resetTime
    };
  }

  /**
   * Clean up old entries (should be called periodically)
   */
  cleanup(): void {
    const now = Date.now();
    const cutoff = now - this.config.windowMs;

    for (const [key, requests] of this.requests.entries()) {
      const validRequests = requests.filter(timestamp => timestamp > cutoff);
      
      if (validRequests.length === 0) {
        this.requests.delete(key);
      } else {
        this.requests.set(key, validRequests);
      }
    }
  }
}

/**
 * Pre-configured rate limiters for different use cases
 */
export class RateLimiters {
  // API rate limiter - 100 requests per minute
  static readonly API = new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100
  });

  // AI service rate limiter - 30 requests per minute
  static readonly AI_SERVICE = new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30
  });

  // Reddit API rate limiter - 60 requests per minute
  static readonly REDDIT_API = new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60
  });

  // User actions rate limiter - 10 actions per minute
  static readonly USER_ACTIONS = new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10
  });

  // Comment posting rate limiter - 5 comments per minute
  static readonly COMMENTS = new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5
  });

  // Vote rate limiter - 20 votes per minute
  static readonly VOTES = new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 20
  });

  // Search rate limiter - 30 searches per minute
  static readonly SEARCH = new RateLimiter({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30
  });

  // Code generation rate limiter - 5 generations per hour
  static readonly CODE_GENERATION = new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5
  });
}

/**
 * Rate limiting middleware for different contexts
 */
export class RateLimitMiddleware {
  /**
   * Create a rate limit check function
   */
  static create(limiter: RateLimiter, options: {
    keyExtractor: (context: any) => string;
    onLimitExceeded?: (result: RateLimitResult) => void;
    onSuccess?: (result: RateLimitResult) => void;
  }) {
    return (context: any) => {
      const identifier = options.keyExtractor(context);
      const result = limiter.checkLimit(identifier);

      if (!result.allowed) {
        options.onLimitExceeded?.(result);
        throw new RateLimitError(
          `Rate limit exceeded. Try again in ${result.retryAfter} seconds.`,
          result
        );
      }

      options.onSuccess?.(result);
      return result;
    };
  }

  /**
   * IP-based rate limiting
   */
  static byIP(limiter: RateLimiter) {
    return this.create(limiter, {
      keyExtractor: (context) => context.ip || 'unknown',
      onLimitExceeded: (result) => {
        console.warn(`Rate limit exceeded for IP. Retry after: ${result.retryAfter}s`);
      }
    });
  }

  /**
   * User-based rate limiting
   */
  static byUser(limiter: RateLimiter) {
    return this.create(limiter, {
      keyExtractor: (context) => context.userId || context.ip || 'anonymous',
      onLimitExceeded: (result) => {
        console.warn(`Rate limit exceeded for user. Retry after: ${result.retryAfter}s`);
      }
    });
  }

  /**
   * Action-based rate limiting
   */
  static byAction(limiter: RateLimiter, action: string) {
    return this.create(limiter, {
      keyExtractor: (context) => `${action}:${context.userId || context.ip}`,
      onLimitExceeded: (result) => {
        console.warn(`Rate limit exceeded for action ${action}. Retry after: ${result.retryAfter}s`);
      }
    });
  }
}

/**
 * Rate limit error class
 */
export class RateLimitError extends Error {
  public readonly rateLimitResult: RateLimitResult;

  constructor(message: string, result: RateLimitResult) {
    super(message);
    this.name = 'RateLimitError';
    this.rateLimitResult = result;
  }
}

/**
 * Rate limit utilities
 */
export class RateLimitUtils {
  /**
   * Format rate limit info for API responses
   */
  static formatHeaders(result: RateLimitResult): Record<string, string> {
    const headers: Record<string, string> = {
      'X-RateLimit-Remaining': result.remaining.toString(),
      'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString()
    };

    if (result.retryAfter) {
      headers['Retry-After'] = result.retryAfter.toString();
    }

    return headers;
  }

  /**
   * Create a human-readable rate limit message
   */
  static formatMessage(result: RateLimitResult, action: string = 'action'): string {
    if (result.allowed) {
      return `${action} completed. ${result.remaining} requests remaining.`;
    }

    const retryAfter = result.retryAfter || 60;
    const minutes = Math.floor(retryAfter / 60);
    const seconds = retryAfter % 60;

    let timeString = '';
    if (minutes > 0) {
      timeString = `${minutes} minute${minutes > 1 ? 's' : ''}`;
      if (seconds > 0) {
        timeString += ` and ${seconds} second${seconds > 1 ? 's' : ''}`;
      }
    } else {
      timeString = `${seconds} second${seconds > 1 ? 's' : ''}`;
    }

    return `Rate limit exceeded for ${action}. Please try again in ${timeString}.`;
  }

  /**
   * Check if an error is a rate limit error
   */
  static isRateLimitError(error: any): error is RateLimitError {
    return error instanceof RateLimitError;
  }

  /**
   * Get rate limit status for multiple limiters
   */
  static getMultipleStatus(identifier: string, limiters: Record<string, RateLimiter>): Record<string, RateLimitResult> {
    const status: Record<string, RateLimitResult> = {};
    
    for (const [name, limiter] of Object.entries(limiters)) {
      status[name] = limiter.getStatus(identifier);
    }

    return status;
  }

  /**
   * Create a composite rate limiter that checks multiple limits
   */
  static createComposite(limiters: RateLimiter[]): RateLimiter {
    return new (class extends RateLimiter {
      checkLimit(identifier: string): RateLimitResult {
        let mostRestrictive: RateLimitResult | null = null;

        for (const limiter of limiters) {
          const result = limiter.checkLimit(identifier);
          
          if (!result.allowed) {
            return result; // Immediately return if any limiter blocks
          }

          if (!mostRestrictive || result.remaining < mostRestrictive.remaining) {
            mostRestrictive = result;
          }
        }

        return mostRestrictive || {
          allowed: true,
          remaining: 0,
          resetTime: Date.now() + 60000
        };
      }
    })({ windowMs: 60000, maxRequests: 100 });
  }
}

/**
 * Cleanup service for rate limiters
 */
export class RateLimitCleanupService {
  private static instance: RateLimitCleanupService;
  private intervalId: NodeJS.Timeout | null = null;
  private limiters: RateLimiter[] = [];

  static getInstance(): RateLimitCleanupService {
    if (!this.instance) {
      this.instance = new RateLimitCleanupService();
    }
    return this.instance;
  }

  /**
   * Register a rate limiter for periodic cleanup
   */
  register(limiter: RateLimiter): void {
    this.limiters.push(limiter);
  }

  /**
   * Start periodic cleanup
   */
  start(intervalMs: number = 5 * 60 * 1000): void { // Default: 5 minutes
    if (this.intervalId) {
      this.stop();
    }

    this.intervalId = setInterval(() => {
      this.cleanup();
    }, intervalMs);
  }

  /**
   * Stop periodic cleanup
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Manually trigger cleanup
   */
  cleanup(): void {
    for (const limiter of this.limiters) {
      limiter.cleanup();
    }
  }
}

// Auto-register common rate limiters for cleanup
const cleanupService = RateLimitCleanupService.getInstance();
cleanupService.register(RateLimiters.API);
cleanupService.register(RateLimiters.AI_SERVICE);
cleanupService.register(RateLimiters.REDDIT_API);
cleanupService.register(RateLimiters.USER_ACTIONS);
cleanupService.register(RateLimiters.COMMENTS);
cleanupService.register(RateLimiters.VOTES);
cleanupService.register(RateLimiters.SEARCH);
cleanupService.register(RateLimiters.CODE_GENERATION);

// Start cleanup service
cleanupService.start();

export default RateLimiter;
