import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  ThumbsUp, 
  ThumbsDown, 
  TrendingUp, 
  Users,
  AlertCircle 
} from 'lucide-react';
import { useCommunityData } from '@/hooks/useCommunityData';

interface VotingSystemProps {
  targetId: string;
  targetType: 'meme' | 'app' | 'comment';
  userId?: string;
  initialUpvotes?: number;
  initialDownvotes?: number;
  showStats?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const VotingSystem = ({ 
  targetId, 
  targetType, 
  userId, 
  initialUpvotes = 0,
  initialDownvotes = 0,
  showStats = true,
  size = 'md',
  className 
}: VotingSystemProps) => {
  const [userVote, setUserVote] = useState<'up' | 'down' | null>(null);
  const [isVoting, setIsVoting] = useState(false);
  
  const { submitVote, useVotes } = useCommunityData(userId);
  
  const { data: votes, isLoading } = useVotes(targetId);
  
  const upvotes = votes?.upvotes ?? initialUpvotes;
  const downvotes = votes?.downvotes ?? initialDownvotes;
  const totalVotes = upvotes + downvotes;
  const voteRatio = totalVotes > 0 ? (upvotes / totalVotes) * 100 : 0;

  const handleVote = async (voteType: 'up' | 'down') => {
    if (!userId) {
      // Could show a login prompt here
      return;
    }

    if (isVoting) return;

    setIsVoting(true);
    
    try {
      await submitVote.mutateAsync({
        userId,
        targetId,
        targetType,
        voteType
      });
      
      setUserVote(userVote === voteType ? null : voteType);
    } catch (error) {
      console.error('Failed to submit vote:', error);
    } finally {
      setIsVoting(false);
    }
  };

  const getButtonSize = () => {
    switch (size) {
      case 'sm': return 'sm';
      case 'lg': return 'lg';
      default: return 'default';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 'h-3 w-3';
      case 'lg': return 'h-5 w-5';
      default: return 'h-4 w-4';
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'sm': return 'text-xs';
      case 'lg': return 'text-base';
      default: return 'text-sm';
    }
  };

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="animate-pulse bg-slate-700 rounded h-8 w-16"></div>
        <div className="animate-pulse bg-slate-700 rounded h-8 w-16"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Voting Buttons */}
      <div className="flex items-center gap-2">
        <Button
          size={getButtonSize()}
          variant={userVote === 'up' ? 'default' : 'outline'}
          onClick={() => handleVote('up')}
          disabled={isVoting || !userId}
          className={`${
            userVote === 'up' 
              ? 'bg-green-600 hover:bg-green-700 text-white' 
              : 'border-green-500/50 text-green-400 hover:bg-green-900/30'
          } ${!userId ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <ThumbsUp className={`${getIconSize()} mr-1`} />
          <span className={getTextSize()}>{upvotes}</span>
        </Button>

        <Button
          size={getButtonSize()}
          variant={userVote === 'down' ? 'default' : 'outline'}
          onClick={() => handleVote('down')}
          disabled={isVoting || !userId}
          className={`${
            userVote === 'down' 
              ? 'bg-red-600 hover:bg-red-700 text-white' 
              : 'border-red-500/50 text-red-400 hover:bg-red-900/30'
          } ${!userId ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <ThumbsDown className={`${getIconSize()} mr-1`} />
          <span className={getTextSize()}>{downvotes}</span>
        </Button>

        {showStats && totalVotes > 0 && (
          <Badge 
            className={`${getTextSize()} ${
              voteRatio >= 70 ? 'bg-green-600/20 text-green-300' :
              voteRatio >= 50 ? 'bg-yellow-600/20 text-yellow-300' :
              'bg-red-600/20 text-red-300'
            }`}
          >
            {Math.round(voteRatio)}% positive
          </Badge>
        )}
      </div>

      {/* Extended Stats */}
      {showStats && size !== 'sm' && (
        <div className="flex items-center gap-4 text-xs text-slate-400">
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            <span>{totalVotes} votes</span>
          </div>
          
          {voteRatio >= 80 && (
            <div className="flex items-center gap-1 text-green-400">
              <TrendingUp className="h-3 w-3" />
              <span>Trending</span>
            </div>
          )}
          
          {totalVotes > 100 && (
            <Badge className="text-xs bg-purple-600/20 text-purple-300">
              Popular
            </Badge>
          )}
        </div>
      )}

      {/* Login Prompt */}
      {!userId && size !== 'sm' && (
        <div className="flex items-center gap-2 text-xs text-slate-500">
          <AlertCircle className="h-3 w-3" />
          <span>Sign in to vote</span>
        </div>
      )}

      {/* Error State */}
      {submitVote.isError && (
        <div className="text-xs text-red-400 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          <span>Failed to submit vote</span>
        </div>
      )}
    </div>
  );
};

export default VotingSystem;
