
import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Brain, ThumbsUp, MessageCircle, Zap } from 'lucide-react';
import MemeCard from './MemeCard';

interface Meme {
  id: string;
  title: string;
  imageUrl: string;
  subreddit: string;
  upvotes: number;
  comments: number;
  trending: boolean;
  appPotential: number;
}

const TrendingMemes = () => {
  const [memes, setMemes] = useState<Meme[]>([]);
  const [selectedMeme, setSelectedMeme] = useState<Meme | null>(null);

  useEffect(() => {
    // Simulate Reddit API call with mock data
    const mockMemes: Meme[] = [
      {
        id: '1',
        title: 'When you fix a bug but create 3 more',
        imageUrl: 'https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400',
        subreddit: 'ProgrammerHumor',
        upvotes: 15420,
        comments: 312,
        trending: true,
        appPotential: 85
      },
      {
        id: '2',
        title: 'Me explaining my code to rubber duck',
        imageUrl: 'https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=400',
        subreddit: 'webdev',
        upvotes: 8934,
        comments: 156,
        trending: true,
        appPotential: 78
      },
      {
        id: '3',
        title: 'CSS centering div in 2024',
        imageUrl: 'https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400',
        subreddit: 'css',
        upvotes: 12567,
        comments: 89,
        trending: false,
        appPotential: 92
      }
    ];
    
    setMemes(mockMemes);
  }, []);

  const handleAnalyzeMeme = (meme: Meme) => {
    setSelectedMeme(meme);
    console.log('Analyzing meme:', meme.title);
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-4xl font-bold text-white mb-4 flex items-center justify-center gap-2">
          <TrendingUp className="h-8 w-8 text-purple-400" />
          Trending Memes
        </h2>
        <p className="text-slate-400 text-lg">
          Hot memes with high app generation potential
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {memes.map((meme) => (
          <MemeCard 
            key={meme.id} 
            meme={meme} 
            onAnalyze={() => handleAnalyzeMeme(meme)}
          />
        ))}
      </div>

      {selectedMeme && (
        <Card className="bg-black/40 border-green-500/50 backdrop-blur-sm">
          <CardHeader>
            <div className="flex items-center gap-2">
              <Brain className="h-6 w-6 text-green-400" />
              <CardTitle className="text-white">AI Analysis: {selectedMeme.title}</CardTitle>
            </div>
            <CardDescription className="text-slate-300">
              Extracting app concepts from meme patterns...
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-green-400 mb-2">Detected Themes:</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge className="bg-green-600/20 text-green-300">Problem Solving</Badge>
                  <Badge className="bg-blue-600/20 text-blue-300">Developer Tools</Badge>
                  <Badge className="bg-purple-600/20 text-purple-300">Productivity</Badge>
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-green-400 mb-2">App Concepts:</h4>
                <ul className="text-slate-300 space-y-1">
                  <li>• Bug Tracker with AI Suggestions</li>
                  <li>• Code Review Assistant</li>
                  <li>• Developer Mood Tracker</li>
                </ul>
              </div>
            </div>
            <div className="flex gap-3">
              <Button className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
                <Zap className="h-4 w-4 mr-2" />
                Generate Full Blueprint
              </Button>
              <Button variant="outline" className="border-green-500/50 text-green-300 hover:bg-green-900/30">
                View Code Templates
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TrendingMemes;
