import { 
  User, 
  Vote, 
  Comment, 
  CommunityChallenge, 
  Leaderboard, 
  LeaderboardEntry,
  CommunityEvent,
  UserStats,
  UserBadge 
} from '@/types/community';
import { GeneratedApp } from '@/types/app';

// Rate limiting for community service
const COMMUNITY_RATE_LIMIT = {
  requests: 100, // requests per minute
  window: 60 * 1000, // 1 minute in milliseconds
};

class CommunityRateLimiter {
  private requests: number[] = [];

  canMakeRequest(): boolean {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < COMMUNITY_RATE_LIMIT.window);
    
    if (this.requests.length >= COMMUNITY_RATE_LIMIT.requests) {
      return false;
    }
    
    this.requests.push(now);
    return true;
  }

  getTimeUntilNextRequest(): number {
    if (this.requests.length < COMMUNITY_RATE_LIMIT.requests) {
      return 0;
    }
    
    const oldestRequest = Math.min(...this.requests);
    return COMMUNITY_RATE_LIMIT.window - (Date.now() - oldestRequest);
  }
}

class CommunityService {
  private rateLimiter = new CommunityRateLimiter();
  private users = new Map<string, User>();
  private votes = new Map<string, Vote[]>();
  private comments = new Map<string, Comment[]>();
  private challenges = new Map<string, CommunityChallenge>();
  private events = new Map<string, CommunityEvent>();

  constructor() {
    this.initializeMockData();
  }

  // User Management
  async getUser(userId: string): Promise<User | null> {
    if (!this.rateLimiter.canMakeRequest()) {
      throw new Error('Rate limit exceeded for community service');
    }

    return this.users.get(userId) || null;
  }

  async createUser(userData: Partial<User>): Promise<User> {
    if (!this.rateLimiter.canMakeRequest()) {
      throw new Error('Rate limit exceeded for community service');
    }

    const user: User = {
      id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      username: userData.username || 'Anonymous',
      email: userData.email || '',
      avatar: userData.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${userData.username}`,
      joinDate: new Date(),
      reputation: 0,
      badge: this.getDefaultBadge(),
      stats: {
        memesAnalyzed: 0,
        appsGenerated: 0,
        communityVotes: 0,
        successfulApps: 0,
        totalDownloads: 0,
      },
      preferences: {
        favoriteSubreddits: [],
        preferredTechStack: [],
        notificationSettings: {
          newTrendingMemes: true,
          analysisComplete: true,
          communityUpdates: true,
          appFeatured: true,
        },
        privacy: {
          profilePublic: true,
          showStats: true,
          allowMessaging: true,
        },
      },
    };

    this.users.set(user.id, user);
    return user;
  }

  async updateUserStats(userId: string, statUpdate: Partial<UserStats>): Promise<User | null> {
    const user = this.users.get(userId);
    if (!user) return null;

    user.stats = { ...user.stats, ...statUpdate };
    user.reputation = this.calculateReputation(user.stats);
    user.badge = this.calculateBadge(user.stats, user.reputation);

    this.users.set(userId, user);
    return user;
  }

  // Voting System
  async submitVote(userId: string, targetId: string, targetType: 'meme' | 'app' | 'comment', voteType: 'up' | 'down'): Promise<Vote> {
    if (!this.rateLimiter.canMakeRequest()) {
      throw new Error('Rate limit exceeded for community service');
    }

    // Check if user already voted on this target
    const existingVotes = this.votes.get(targetId) || [];
    const existingVote = existingVotes.find(vote => vote.userId === userId);

    if (existingVote) {
      // Update existing vote
      existingVote.voteType = voteType;
      return existingVote;
    }

    // Create new vote
    const vote: Vote = {
      id: `vote_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      targetId,
      targetType,
      voteType,
      createdAt: new Date(),
    };

    existingVotes.push(vote);
    this.votes.set(targetId, existingVotes);

    // Update user stats
    await this.updateUserStats(userId, { 
      communityVotes: (await this.getUser(userId))?.stats.communityVotes + 1 || 1 
    });

    return vote;
  }

  async getVotes(targetId: string): Promise<{ upvotes: number; downvotes: number; userVote?: Vote }> {
    const votes = this.votes.get(targetId) || [];
    const upvotes = votes.filter(vote => vote.voteType === 'up').length;
    const downvotes = votes.filter(vote => vote.voteType === 'down').length;

    return { upvotes, downvotes };
  }

  // Comment System
  async addComment(userId: string, targetId: string, targetType: 'meme' | 'app', content: string, parentCommentId?: string): Promise<Comment> {
    if (!this.rateLimiter.canMakeRequest()) {
      throw new Error('Rate limit exceeded for community service');
    }

    const user = await this.getUser(userId);
    if (!user) throw new Error('User not found');

    const comment: Comment = {
      id: `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      username: user.username,
      avatar: user.avatar,
      targetId,
      targetType,
      content,
      parentCommentId,
      upvotes: 0,
      downvotes: 0,
      createdAt: new Date(),
      replies: [],
    };

    const comments = this.comments.get(targetId) || [];
    comments.push(comment);
    this.comments.set(targetId, comments);

    return comment;
  }

  async getComments(targetId: string): Promise<Comment[]> {
    const comments = this.comments.get(targetId) || [];
    
    // Organize comments into threads (parent comments with replies)
    const parentComments = comments.filter(comment => !comment.parentCommentId);
    const replies = comments.filter(comment => comment.parentCommentId);

    parentComments.forEach(parent => {
      parent.replies = replies.filter(reply => reply.parentCommentId === parent.id);
    });

    return parentComments.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  // Leaderboard System
  async getLeaderboard(period: 'daily' | 'weekly' | 'monthly' | 'all-time', category: 'apps-generated' | 'downloads' | 'community-votes' | 'reputation'): Promise<Leaderboard> {
    const users = Array.from(this.users.values());
    
    let sortedUsers: User[];
    switch (category) {
      case 'apps-generated':
        sortedUsers = users.sort((a, b) => b.stats.appsGenerated - a.stats.appsGenerated);
        break;
      case 'downloads':
        sortedUsers = users.sort((a, b) => b.stats.totalDownloads - a.stats.totalDownloads);
        break;
      case 'community-votes':
        sortedUsers = users.sort((a, b) => b.stats.communityVotes - a.stats.communityVotes);
        break;
      case 'reputation':
      default:
        sortedUsers = users.sort((a, b) => b.reputation - a.reputation);
        break;
    }

    const entries: LeaderboardEntry[] = sortedUsers.slice(0, 50).map((user, index) => ({
      rank: index + 1,
      userId: user.id,
      username: user.username,
      avatar: user.avatar,
      score: this.getScoreForCategory(user, category),
      change: 0, // Would calculate based on previous period
      badge: user.badge,
    }));

    return {
      period,
      category,
      entries,
      lastUpdated: new Date(),
    };
  }

  // Challenge System
  async getChallenges(): Promise<CommunityChallenge[]> {
    return Array.from(this.challenges.values());
  }

  async getActiveChallenge(): Promise<CommunityChallenge | null> {
    const challenges = Array.from(this.challenges.values());
    return challenges.find(challenge => challenge.status === 'active') || null;
  }

  async joinChallenge(challengeId: string, userId: string): Promise<boolean> {
    const challenge = this.challenges.get(challengeId);
    if (!challenge || challenge.status !== 'active') return false;

    if (!challenge.participants.includes(userId)) {
      challenge.participants.push(userId);
      this.challenges.set(challengeId, challenge);
    }

    return true;
  }

  // Event System
  async getUpcomingEvents(): Promise<CommunityEvent[]> {
    const events = Array.from(this.events.values());
    const now = new Date();
    
    return events
      .filter(event => event.startTime > now)
      .sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }

  async registerForEvent(eventId: string, userId: string): Promise<boolean> {
    const event = this.events.get(eventId);
    if (!event) return false;

    if (event.maxParticipants && event.registeredParticipants.length >= event.maxParticipants) {
      return false;
    }

    if (!event.registeredParticipants.includes(userId)) {
      event.registeredParticipants.push(userId);
      this.events.set(eventId, event);
    }

    return true;
  }

  // Helper Methods
  private getDefaultBadge(): UserBadge {
    return {
      title: 'Newcomer',
      color: '#6B7280',
      icon: '🌱',
      requirements: 'Join the community',
    };
  }

  private calculateReputation(stats: UserStats): number {
    return (
      stats.appsGenerated * 10 +
      stats.memesAnalyzed * 2 +
      stats.communityVotes * 1 +
      stats.successfulApps * 50 +
      Math.floor(stats.totalDownloads / 100)
    );
  }

  private calculateBadge(stats: UserStats, reputation: number): UserBadge {
    if (reputation >= 1000) {
      return { title: 'Meme Legend', color: '#F59E0B', icon: '👑', requirements: '1000+ reputation' };
    } else if (reputation >= 500) {
      return { title: 'App Master', color: '#8B5CF6', icon: '🚀', requirements: '500+ reputation' };
    } else if (reputation >= 100) {
      return { title: 'Creator', color: '#10B981', icon: '⭐', requirements: '100+ reputation' };
    } else if (stats.appsGenerated >= 5) {
      return { title: 'Builder', color: '#3B82F6', icon: '🔨', requirements: '5+ apps generated' };
    } else {
      return this.getDefaultBadge();
    }
  }

  private getScoreForCategory(user: User, category: string): number {
    switch (category) {
      case 'apps-generated':
        return user.stats.appsGenerated;
      case 'downloads':
        return user.stats.totalDownloads;
      case 'community-votes':
        return user.stats.communityVotes;
      case 'reputation':
      default:
        return user.reputation;
    }
  }

  private initializeMockData(): void {
    // Create some mock users
    const mockUsers: User[] = [
      {
        id: 'user1',
        username: 'DevMaster2000',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=DevMaster2000',
        joinDate: new Date('2024-01-15'),
        reputation: 1250,
        badge: { title: 'Meme Legend', color: '#F59E0B', icon: '👑', requirements: '1000+ reputation' },
        stats: {
          memesAnalyzed: 150,
          appsGenerated: 25,
          communityVotes: 500,
          successfulApps: 8,
          totalDownloads: 12500,
        },
        preferences: {
          favoriteSubreddits: ['ProgrammerHumor', 'webdev'],
          preferredTechStack: ['React', 'Node.js', 'TypeScript'],
          notificationSettings: {
            newTrendingMemes: true,
            analysisComplete: true,
            communityUpdates: true,
            appFeatured: true,
          },
          privacy: {
            profilePublic: true,
            showStats: true,
            allowMessaging: true,
          },
        },
      },
      {
        id: 'user2',
        username: 'CodeWhisperer',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=CodeWhisperer',
        joinDate: new Date('2024-02-01'),
        reputation: 780,
        badge: { title: 'App Master', color: '#8B5CF6', icon: '🚀', requirements: '500+ reputation' },
        stats: {
          memesAnalyzed: 89,
          appsGenerated: 18,
          communityVotes: 320,
          successfulApps: 5,
          totalDownloads: 8900,
        },
        preferences: {
          favoriteSubreddits: ['javascript', 'reactjs'],
          preferredTechStack: ['React', 'Python', 'PostgreSQL'],
          notificationSettings: {
            newTrendingMemes: true,
            analysisComplete: true,
            communityUpdates: false,
            appFeatured: true,
          },
          privacy: {
            profilePublic: true,
            showStats: true,
            allowMessaging: true,
          },
        },
      },
    ];

    mockUsers.forEach(user => this.users.set(user.id, user));

    // Create mock challenge
    const mockChallenge: CommunityChallenge = {
      id: 'challenge1',
      title: 'Meme-to-App Speed Challenge',
      description: 'Create the most innovative app from a trending meme in 48 hours!',
      rules: [
        'Must use a meme from the trending section',
        'App must be functional (at least MVP)',
        'Submit within 48 hours',
        'Open source code required',
      ],
      startDate: new Date(Date.now() + 86400000), // Tomorrow
      endDate: new Date(Date.now() + 86400000 * 3), // 3 days from now
      prizes: [
        { rank: 1, title: 'Golden Meme Trophy', description: 'Featured on homepage for 1 month', value: '$500' },
        { rank: 2, title: 'Silver Code Medal', description: 'Featured in newsletter', value: '$250' },
        { rank: 3, title: 'Bronze Builder Badge', description: 'Special community badge', value: '$100' },
      ],
      participants: ['user1', 'user2'],
      submissions: [],
      status: 'upcoming',
    };

    this.challenges.set(mockChallenge.id, mockChallenge);

    // Create mock event
    const mockEvent: CommunityEvent = {
      id: 'event1',
      title: 'MemeForge Workshop: From Meme to MVP',
      description: 'Learn how to rapidly prototype apps from meme concepts',
      type: 'workshop',
      startTime: new Date(Date.now() + 86400000 * 7), // Next week
      endTime: new Date(Date.now() + 86400000 * 7 + 7200000), // 2 hours later
      location: 'online',
      maxParticipants: 50,
      registeredParticipants: ['user1'],
      organizer: 'MemeForge Team',
      tags: ['workshop', 'beginner-friendly', 'hands-on'],
    };

    this.events.set(mockEvent.id, mockEvent);
  }

  // Get rate limit status
  getRateLimitStatus() {
    return {
      requestsRemaining: Math.max(0, COMMUNITY_RATE_LIMIT.requests - this.rateLimiter['requests'].length),
      resetTime: this.rateLimiter.getTimeUntilNextRequest(),
    };
  }
}

export const communityService = new CommunityService();
export default communityService;
