import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  Brain, 
  Lightbulb, 
  Target, 
  DollarSign, 
  Users, 
  Clock, 
  Zap,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { Meme, MemeAnalysis } from '@/types/meme';
import { GeneratedApp } from '@/types/app';
import { useMemeAnalysis } from '@/hooks/useMemeAnalysis';

interface ConceptGeneratorProps {
  meme: Meme;
  onConceptGenerated?: (app: GeneratedApp) => void;
  className?: string;
}

const ConceptGenerator = ({ meme, onConceptGenerated, className }: ConceptGeneratorProps) => {
  const [selectedConceptIndex, setSelectedConceptIndex] = useState(0);
  const { 
    analyzeMeme, 
    generateAppConcept, 
    analyzeAndGenerate,
    currentAnalysis, 
    currentApp,
    getAnalysisProgress,
    isProcessing,
    getError,
    getRecommendations
  } = useMemeAnalysis();

  const handleAnalyze = () => {
    analyzeMeme.mutate(meme);
  };

  const handleGenerateConcept = () => {
    if (currentAnalysis) {
      generateAppConcept.mutate({ meme, analysis: currentAnalysis });
    }
  };

  const handleFullGeneration = () => {
    analyzeAndGenerate.mutate(meme, {
      onSuccess: ({ app }) => {
        onConceptGenerated?.(app);
      }
    });
  };

  const progress = getAnalysisProgress();
  const error = getError();
  const recommendations = getRecommendations(meme);

  const getProgressValue = () => {
    switch (progress) {
      case 'analyzing': return 25;
      case 'generating': return 75;
      case 'creating-template': return 90;
      case 'processing': return 50;
      default: return 0;
    }
  };

  const getProgressText = () => {
    switch (progress) {
      case 'analyzing': return 'Analyzing meme patterns and themes...';
      case 'generating': return 'Generating app concepts and features...';
      case 'creating-template': return 'Creating code templates...';
      case 'processing': return 'Processing your request...';
      default: return 'Ready to analyze';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 border-purple-500/30 backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-center gap-3">
            <Brain className="h-8 w-8 text-purple-400" />
            <div>
              <CardTitle className="text-white text-2xl">AI Concept Generator</CardTitle>
              <CardDescription className="text-slate-300">
                Transform "{meme.title}" into innovative app concepts
              </CardDescription>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Progress Indicator */}
      {isProcessing() && (
        <Card className="bg-black/30 border-blue-500/50 backdrop-blur-sm">
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-blue-300 font-medium">{getProgressText()}</span>
                <span className="text-blue-400">{getProgressValue()}%</span>
              </div>
              <Progress value={getProgressValue()} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Card className="bg-red-900/30 border-red-500/50 backdrop-blur-sm">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-300">
              <AlertCircle className="h-5 w-5" />
              <span>{error.message}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        <Button
          onClick={handleAnalyze}
          disabled={isProcessing()}
          className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
        >
          <Brain className="h-4 w-4 mr-2" />
          {analyzeMeme.isPending ? 'Analyzing...' : 'Analyze Meme'}
        </Button>

        {currentAnalysis && (
          <Button
            onClick={handleGenerateConcept}
            disabled={isProcessing()}
            className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
          >
            <Lightbulb className="h-4 w-4 mr-2" />
            {generateAppConcept.isPending ? 'Generating...' : 'Generate Concept'}
          </Button>
        )}

        <Button
          onClick={handleFullGeneration}
          disabled={isProcessing()}
          className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
        >
          <Zap className="h-4 w-4 mr-2" />
          {analyzeAndGenerate.isPending ? 'Processing...' : 'Full Generation'}
        </Button>
      </div>

      {/* Analysis Results */}
      {currentAnalysis && (
        <Card className="bg-black/30 border-green-500/50 backdrop-blur-sm">
          <CardHeader>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-6 w-6 text-green-400" />
              <CardTitle className="text-white">Analysis Complete</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Themes */}
            <div>
              <h4 className="font-semibold text-green-400 mb-2 flex items-center gap-2">
                <Target className="h-4 w-4" />
                Detected Themes
              </h4>
              <div className="flex flex-wrap gap-2">
                {currentAnalysis.themes.map((theme, index) => (
                  <Badge key={index} className="bg-green-600/20 text-green-300 border-green-500/30">
                    {theme}
                  </Badge>
                ))}
              </div>
            </div>

            <Separator className="bg-slate-700" />

            {/* Tech Stack */}
            <div>
              <h4 className="font-semibold text-blue-400 mb-2">Recommended Tech Stack</h4>
              <div className="flex flex-wrap gap-2">
                {currentAnalysis.techStack.map((tech, index) => (
                  <Badge key={index} className="bg-blue-600/20 text-blue-300 border-blue-500/30">
                    {tech}
                  </Badge>
                ))}
              </div>
            </div>

            <Separator className="bg-slate-700" />

            {/* Concepts */}
            <div>
              <h4 className="font-semibold text-purple-400 mb-3">App Concepts</h4>
              <div className="grid gap-3">
                {currentAnalysis.concepts.map((concept, index) => (
                  <Card 
                    key={index} 
                    className={`cursor-pointer transition-all duration-200 ${
                      selectedConceptIndex === index 
                        ? 'bg-purple-900/40 border-purple-400/50' 
                        : 'bg-black/20 border-slate-700/50 hover:border-purple-500/30'
                    }`}
                    onClick={() => setSelectedConceptIndex(index)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h5 className="font-semibold text-white">{concept.name}</h5>
                        {selectedConceptIndex === index && (
                          <CheckCircle className="h-5 w-5 text-purple-400" />
                        )}
                      </div>
                      <p className="text-slate-300 text-sm mb-3">{concept.description}</p>
                      
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                          <Users className="h-4 w-4 text-slate-400" />
                          <span className="text-slate-400">{concept.targetAudience}</span>
                        </div>
                        
                        <div className="flex flex-wrap gap-1">
                          {concept.features.slice(0, 3).map((feature, featureIndex) => (
                            <Badge 
                              key={featureIndex} 
                              className="text-xs bg-slate-700/50 text-slate-300"
                            >
                              {feature}
                            </Badge>
                          ))}
                          {concept.features.length > 3 && (
                            <Badge className="text-xs bg-slate-700/50 text-slate-300">
                              +{concept.features.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <Separator className="bg-slate-700" />

            {/* Analysis Metadata */}
            <div className="grid md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-slate-400" />
                <span className="text-slate-400">Estimated Time:</span>
                <span className="text-white">{currentAnalysis.estimatedTime}</span>
              </div>
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-slate-400" />
                <span className="text-slate-400">Difficulty:</span>
                <Badge className={`text-xs ${
                  currentAnalysis.difficulty === 'Easy' ? 'bg-green-600/20 text-green-300' :
                  currentAnalysis.difficulty === 'Medium' ? 'bg-yellow-600/20 text-yellow-300' :
                  'bg-red-600/20 text-red-300'
                }`}>
                  {currentAnalysis.difficulty}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-slate-400" />
                <span className="text-slate-400">Market Potential:</span>
                <span className="text-white">{currentAnalysis.marketPotential}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card className="bg-black/30 border-yellow-500/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-yellow-400 flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              AI Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start gap-2 text-slate-300">
                  <span className="text-yellow-400 mt-1">•</span>
                  <span>{recommendation}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Generated App Display */}
      {currentApp && (
        <Card className="bg-gradient-to-r from-green-900/50 to-blue-900/50 border-green-500/30 backdrop-blur-sm">
          <CardHeader>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-6 w-6 text-green-400" />
              <CardTitle className="text-white">App Concept Generated!</CardTitle>
            </div>
            <CardDescription className="text-slate-300">
              Your meme has been transformed into a complete app concept
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-2xl font-bold text-white mb-2">{currentApp.name}</h3>
              <p className="text-slate-300 mb-4">{currentApp.description}</p>
              
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-green-400 mb-2">Key Features</h4>
                  <ul className="space-y-1">
                    {currentApp.features.slice(0, 5).map((feature, index) => (
                      <li key={index} className="text-slate-300 text-sm flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 text-green-400" />
                        {feature.name}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold text-blue-400 mb-2">Tech Stack</h4>
                  <div className="flex flex-wrap gap-1">
                    {currentApp.techStack.map((tech, index) => (
                      <Badge key={index} className="text-xs bg-blue-600/20 text-blue-300">
                        {tech}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ConceptGenerator;
