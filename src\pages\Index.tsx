
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Zap, Rocket, Brain, Users, Trophy, TrendingUp } from 'lucide-react';
import TrendingMemes from '@/components/memes/TrendingMemes';
import HallOfFame from '@/components/showcase/HallOfFame';

const Index = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('discover');

  const features = [
    {
      icon: <TrendingUp className="h-6 w-6" />,
      title: "Meme Discovery",
      description: "Browse trending memes from Reddit and viral content"
    },
    {
      icon: <Brain className="h-6 w-6" />,
      title: "AI Analysis",
      description: "Extract app concepts and themes from meme culture"
    },
    {
      icon: <Rocket className="h-6 w-6" />,
      title: "App Generation",
      description: "Transform memes into functional app blueprints"
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: "Community Voting",
      description: "Rate, discuss, and collaborate on meme-apps"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-purple-800/30 bg-black/20 backdrop-blur-lg">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Zap className="h-8 w-8 text-purple-400" />
            <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              MemeForge
            </h1>
          </div>
          <nav className="hidden md:flex space-x-6">
            <Button 
              variant={activeTab === 'discover' ? 'default' : 'ghost'} 
              onClick={() => setActiveTab('discover')}
              className="text-white hover:text-purple-300"
            >
              Discover
            </Button>
            <Button 
              variant={activeTab === 'generate' ? 'default' : 'ghost'} 
              onClick={() => setActiveTab('generate')}
              className="text-white hover:text-purple-300"
            >
              Generate
            </Button>
            <Button 
              variant={activeTab === 'community' ? 'default' : 'ghost'} 
              onClick={() => setActiveTab('community')}
              className="text-white hover:text-purple-300"
            >
              Community
            </Button>
            <Button 
              variant={activeTab === 'halloffame' ? 'default' : 'ghost'} 
              onClick={() => setActiveTab('halloffame')}
              className="text-white hover:text-purple-300"
            >
              <Trophy className="h-4 w-4 mr-1" />
              Hall of Fame
            </Button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <div className="max-w-4xl mx-auto">
          <Badge className="mb-4 bg-purple-600/20 text-purple-300 border-purple-500/30">
            🔥 Transform Memes into Reality
          </Badge>
          <h2 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent">
            Meme to App
          </h2>
          <p className="text-xl text-slate-300 mb-8 leading-relaxed">
            The first platform that transforms viral internet culture into functional applications. 
            From Reddit memes to real apps - powered by AI and community creativity.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              onClick={() => navigate('/generate')}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold px-8 py-3 rounded-lg shadow-lg hover:shadow-purple-500/25 transition-all duration-300"
            >
              <Rocket className="h-5 w-5 mr-2" />
              Start Creating
            </Button>
            <Button
              size="lg"
              variant="outline"
              onClick={() => navigate('/browse')}
              className="border-purple-500/50 text-purple-300 hover:bg-purple-900/30 px-8 py-3"
            >
              <Brain className="h-5 w-5 mr-2" />
              How It Works
            </Button>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="container mx-auto px-4 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="bg-black/30 border-purple-800/30 backdrop-blur-sm hover:bg-black/40 transition-all duration-300 hover:border-purple-600/50">
              <CardHeader>
                <div className="text-purple-400 mb-2">{feature.icon}</div>
                <CardTitle className="text-white">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-slate-400">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Dynamic Content Based on Active Tab */}
      <section className="container mx-auto px-4 py-16">
        {activeTab === 'discover' && <TrendingMemes />}
        {activeTab === 'halloffame' && <HallOfFame />}
        {activeTab === 'generate' && (
          <div className="text-center py-16">
            <h3 className="text-3xl font-bold text-white mb-4">AI App Generator</h3>
            <p className="text-slate-400 mb-8">Select a meme above to start generating your app concept</p>
            <Button className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
              <Brain className="h-5 w-5 mr-2" />
              Generate App Concept
            </Button>
          </div>
        )}
        {activeTab === 'community' && (
          <div className="text-center py-16">
            <h3 className="text-3xl font-bold text-white mb-4">Community Hub</h3>
            <p className="text-slate-400 mb-8">Connect with fellow meme-to-app creators</p>
            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <Card className="bg-black/30 border-purple-800/30">
                <CardHeader>
                  <CardTitle className="text-white">Active Creators</CardTitle>
                  <CardDescription className="text-3xl font-bold text-purple-400">1,337</CardDescription>
                </CardHeader>
              </Card>
              <Card className="bg-black/30 border-purple-800/30">
                <CardHeader>
                  <CardTitle className="text-white">Apps Generated</CardTitle>
                  <CardDescription className="text-3xl font-bold text-green-400">42,069</CardDescription>
                </CardHeader>
              </Card>
              <Card className="bg-black/30 border-purple-800/30">
                <CardHeader>
                  <CardTitle className="text-white">Memes Analyzed</CardTitle>
                  <CardDescription className="text-3xl font-bold text-pink-400">∞</CardDescription>
                </CardHeader>
              </Card>
            </div>
          </div>
        )}
      </section>

      {/* Footer */}
      <footer className="border-t border-purple-800/30 bg-black/20 backdrop-blur-lg mt-16">
        <div className="container mx-auto px-4 py-8 text-center">
          <p className="text-slate-400">
            Made with 💜 by the MemeForge community • Transforming internet culture since 2024
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
