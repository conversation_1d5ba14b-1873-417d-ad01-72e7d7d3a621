import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  MessageCircle, 
  Reply, 
  Send, 
  MoreHorizontal,
  Clock,
  Heart,
  Flag,
  Edit,
  Trash2
} from 'lucide-react';
import { Comment } from '@/types/community';
import { useCommunityData } from '@/hooks/useCommunityData';
import VotingSystem from './VotingSystem';

interface CommentSectionProps {
  targetId: string;
  targetType: 'meme' | 'app';
  userId?: string;
  className?: string;
}

interface CommentItemProps {
  comment: Comment;
  userId?: string;
  onReply: (parentId: string) => void;
  level?: number;
}

const CommentItem = ({ comment, userId, onReply, level = 0 }: CommentItemProps) => {
  const [showReplies, setShowReplies] = useState(true);
  const maxLevel = 3; // Maximum nesting level

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  return (
    <div className={`${level > 0 ? 'ml-6 border-l border-slate-700 pl-4' : ''}`}>
      <div className="flex gap-3 mb-3">
        <Avatar className="h-8 w-8">
          <AvatarImage src={comment.avatar} alt={comment.username} />
          <AvatarFallback>{comment.username.charAt(0).toUpperCase()}</AvatarFallback>
        </Avatar>
        
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <span className="font-semibold text-white text-sm">{comment.username}</span>
            <div className="flex items-center gap-1 text-xs text-slate-400">
              <Clock className="h-3 w-3" />
              <span>{formatTimeAgo(comment.createdAt)}</span>
            </div>
            {comment.updatedAt && comment.updatedAt > comment.createdAt && (
              <Badge className="text-xs bg-slate-600/50 text-slate-300">edited</Badge>
            )}
          </div>
          
          <p className="text-slate-300 text-sm leading-relaxed">{comment.content}</p>
          
          <div className="flex items-center gap-4">
            <VotingSystem
              targetId={comment.id}
              targetType="comment"
              userId={userId}
              initialUpvotes={comment.upvotes}
              initialDownvotes={comment.downvotes}
              size="sm"
              showStats={false}
            />
            
            {level < maxLevel && (
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onReply(comment.id)}
                className="text-slate-400 hover:text-white h-6 px-2"
              >
                <Reply className="h-3 w-3 mr-1" />
                Reply
              </Button>
            )}
            
            <Button
              size="sm"
              variant="ghost"
              className="text-slate-400 hover:text-red-400 h-6 px-2"
            >
              <Flag className="h-3 w-3" />
            </Button>
            
            {userId === comment.userId && (
              <>
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-slate-400 hover:text-blue-400 h-6 px-2"
                >
                  <Edit className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  className="text-slate-400 hover:text-red-400 h-6 px-2"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
      
      {/* Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-3">
          {comment.replies.length > 3 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowReplies(!showReplies)}
              className="text-blue-400 hover:text-blue-300 mb-2 h-6"
            >
              {showReplies ? 'Hide' : 'Show'} {comment.replies.length} replies
            </Button>
          )}
          
          {showReplies && (
            <div className="space-y-3">
              {comment.replies.map((reply) => (
                <CommentItem
                  key={reply.id}
                  comment={reply}
                  userId={userId}
                  onReply={onReply}
                  level={level + 1}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const CommentSection = ({ targetId, targetType, userId, className }: CommentSectionProps) => {
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  
  const { addComment, useComments } = useCommunityData(userId);
  const { data: comments = [], isLoading, refetch } = useComments(targetId);

  const handleSubmitComment = async () => {
    if (!newComment.trim() || !userId) return;

    try {
      await addComment.mutateAsync({
        userId,
        targetId,
        targetType,
        content: newComment.trim(),
      });
      
      setNewComment('');
      refetch();
    } catch (error) {
      console.error('Failed to add comment:', error);
    }
  };

  const handleSubmitReply = async () => {
    if (!replyContent.trim() || !userId || !replyingTo) return;

    try {
      await addComment.mutateAsync({
        userId,
        targetId,
        targetType,
        content: replyContent.trim(),
        parentCommentId: replyingTo,
      });
      
      setReplyContent('');
      setReplyingTo(null);
      refetch();
    } catch (error) {
      console.error('Failed to add reply:', error);
    }
  };

  const handleReply = (parentId: string) => {
    setReplyingTo(parentId);
  };

  if (isLoading) {
    return (
      <Card className={`bg-black/30 border-slate-700/50 backdrop-blur-sm ${className}`}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-slate-700 rounded w-1/4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex gap-3">
                  <div className="h-8 w-8 bg-slate-700 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-3 bg-slate-700 rounded w-1/3"></div>
                    <div className="h-4 bg-slate-700 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`bg-black/30 border-slate-700/50 backdrop-blur-sm ${className}`}>
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          Comments ({comments.length})
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Add Comment */}
        {userId ? (
          <div className="space-y-3">
            <Textarea
              placeholder="Share your thoughts..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400 resize-none"
              rows={3}
            />
            <div className="flex justify-end">
              <Button
                onClick={handleSubmitComment}
                disabled={!newComment.trim() || addComment.isPending}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Send className="h-4 w-4 mr-2" />
                {addComment.isPending ? 'Posting...' : 'Post Comment'}
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-4 text-slate-400">
            <MessageCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Sign in to join the conversation</p>
          </div>
        )}

        <Separator className="bg-slate-700" />

        {/* Comments List */}
        {comments.length > 0 ? (
          <ScrollArea className="h-96">
            <div className="space-y-6">
              {comments.map((comment) => (
                <CommentItem
                  key={comment.id}
                  comment={comment}
                  userId={userId}
                  onReply={handleReply}
                />
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className="text-center py-8 text-slate-400">
            <MessageCircle className="h-12 w-12 mx-auto mb-3 opacity-30" />
            <p className="text-lg font-medium mb-1">No comments yet</p>
            <p className="text-sm">Be the first to share your thoughts!</p>
          </div>
        )}

        {/* Reply Modal/Inline */}
        {replyingTo && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <Card className="bg-slate-900 border-slate-700 w-full max-w-md">
              <CardHeader>
                <CardTitle className="text-white">Reply to Comment</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  placeholder="Write your reply..."
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                  rows={3}
                />
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setReplyingTo(null);
                      setReplyContent('');
                    }}
                    className="border-slate-600 text-slate-300"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSubmitReply}
                    disabled={!replyContent.trim() || addComment.isPending}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                  >
                    <Reply className="h-4 w-4 mr-2" />
                    Reply
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CommentSection;
