import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Users, 
  Trophy, 
  MessageCircle, 
  TrendingUp, 
  Star,
  Calendar,
  Award,
  Crown,
  Zap
} from 'lucide-react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { useCommunityData } from '@/hooks/useCommunityData';

const Community = () => {
  const [activeTab, setActiveTab] = useState('leaderboard');
  const { useLeaderboard, useUpcomingEvents, getCommunityStats } = useCommunityData();
  
  const { data: leaderboard, isLoading: leaderboardLoading } = useLeaderboard('all-time', 'reputation');
  const { data: events, isLoading: eventsLoading } = useUpcomingEvents();
  
  const communityStats = getCommunityStats();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-600 bg-clip-text text-transparent">
            Community Hub
          </h1>
          <p className="text-xl text-slate-300 mb-6 max-w-2xl mx-auto">
            Connect with fellow creators, compete in challenges, and celebrate the best meme-to-app transformations
          </p>
        </div>

        {/* Community Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="bg-black/30 border-blue-500/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <Users className="h-8 w-8 mx-auto mb-2 text-blue-400" />
              <div className="text-2xl font-bold text-white">{communityStats.totalUsers}</div>
              <div className="text-slate-400 text-sm">Active Creators</div>
            </CardContent>
          </Card>
          
          <Card className="bg-black/30 border-green-500/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <Zap className="h-8 w-8 mx-auto mb-2 text-green-400" />
              <div className="text-2xl font-bold text-white">1,337</div>
              <div className="text-slate-400 text-sm">Apps Generated</div>
            </CardContent>
          </Card>
          
          <Card className="bg-black/30 border-purple-500/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <Trophy className="h-8 w-8 mx-auto mb-2 text-purple-400" />
              <div className="text-2xl font-bold text-white">{communityStats.activeChallenges}</div>
              <div className="text-slate-400 text-sm">Active Challenges</div>
            </CardContent>
          </Card>
          
          <Card className="bg-black/30 border-yellow-500/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <Calendar className="h-8 w-8 mx-auto mb-2 text-yellow-400" />
              <div className="text-2xl font-bold text-white">{communityStats.upcomingEvents}</div>
              <div className="text-slate-400 text-sm">Upcoming Events</div>
            </CardContent>
          </Card>
        </div>

        {/* Community Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-black/30 border border-slate-700 mb-8">
            <TabsTrigger value="leaderboard" className="data-[state=active]:bg-purple-600/30">
              <Trophy className="h-4 w-4 mr-2" />
              Leaderboard
            </TabsTrigger>
            <TabsTrigger value="discussions" className="data-[state=active]:bg-purple-600/30">
              <MessageCircle className="h-4 w-4 mr-2" />
              Discussions
            </TabsTrigger>
            <TabsTrigger value="challenges" className="data-[state=active]:bg-purple-600/30">
              <Award className="h-4 w-4 mr-2" />
              Challenges
            </TabsTrigger>
            <TabsTrigger value="events" className="data-[state=active]:bg-purple-600/30">
              <Calendar className="h-4 w-4 mr-2" />
              Events
            </TabsTrigger>
          </TabsList>

          {/* Leaderboard Tab */}
          <TabsContent value="leaderboard" className="space-y-6">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Crown className="h-6 w-6 text-yellow-400" />
                  Top Creators - All Time
                </CardTitle>
                <CardDescription className="text-slate-300">
                  Ranked by reputation and community contributions
                </CardDescription>
              </CardHeader>
              <CardContent>
                {leaderboardLoading ? (
                  <div className="space-y-4">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <div key={i} className="animate-pulse flex items-center gap-4 p-4 bg-slate-800/30 rounded-lg">
                        <div className="h-12 w-12 bg-slate-700 rounded-full"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-4 bg-slate-700 rounded w-1/3"></div>
                          <div className="h-3 bg-slate-700 rounded w-1/4"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {leaderboard?.entries.slice(0, 10).map((entry, index) => (
                      <div key={entry.userId} className="flex items-center gap-4 p-4 bg-slate-800/30 rounded-lg hover:bg-slate-800/50 transition-colors">
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
                            index === 0 ? 'bg-yellow-600 text-white' :
                            index === 1 ? 'bg-gray-400 text-white' :
                            index === 2 ? 'bg-amber-600 text-white' :
                            'bg-slate-600 text-slate-300'
                          }`}>
                            {entry.rank}
                          </div>
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={entry.avatar} alt={entry.username} />
                            <AvatarFallback>{entry.username.charAt(0).toUpperCase()}</AvatarFallback>
                          </Avatar>
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold text-white">{entry.username}</h3>
                            <Badge 
                              className="text-xs"
                              style={{ 
                                backgroundColor: `${entry.badge.color}20`, 
                                color: entry.badge.color,
                                borderColor: `${entry.badge.color}50`
                              }}
                            >
                              {entry.badge.icon} {entry.badge.title}
                            </Badge>
                          </div>
                          <p className="text-slate-400 text-sm">{entry.score} reputation points</p>
                        </div>
                        
                        <div className="text-right">
                          <div className="text-green-400 font-semibold">
                            {entry.change > 0 ? '+' : ''}{entry.change}
                          </div>
                          <div className="text-slate-500 text-xs">this week</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Discussions Tab */}
          <TabsContent value="discussions" className="space-y-6">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white">Community Discussions</CardTitle>
                <CardDescription className="text-slate-300">
                  Join conversations about memes, apps, and development
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <MessageCircle className="h-16 w-16 mx-auto mb-4 text-slate-400 opacity-50" />
                  <h3 className="text-xl font-semibold text-white mb-2">Discussions Coming Soon</h3>
                  <p className="text-slate-400 mb-4">
                    We're building an amazing discussion platform for the community
                  </p>
                  <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                    Get Notified
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Challenges Tab */}
          <TabsContent value="challenges" className="space-y-6">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white">Active Challenges</CardTitle>
                <CardDescription className="text-slate-300">
                  Compete with other creators and win amazing prizes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Award className="h-16 w-16 mx-auto mb-4 text-slate-400 opacity-50" />
                  <h3 className="text-xl font-semibold text-white mb-2">Challenges Coming Soon</h3>
                  <p className="text-slate-400 mb-4">
                    Epic coding challenges and meme competitions are on the way
                  </p>
                  <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                    Join Waitlist
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Events Tab */}
          <TabsContent value="events" className="space-y-6">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white">Upcoming Events</CardTitle>
                <CardDescription className="text-slate-300">
                  Workshops, hackathons, and community meetups
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Calendar className="h-16 w-16 mx-auto mb-4 text-slate-400 opacity-50" />
                  <h3 className="text-xl font-semibold text-white mb-2">Events Coming Soon</h3>
                  <p className="text-slate-400 mb-4">
                    Amazing workshops and community events are being planned
                  </p>
                  <Button className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
                    Stay Updated
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <Footer />
    </div>
  );
};

export default Community;
