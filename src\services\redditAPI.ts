import { RedditPostData, Meme, SUBREDDIT_CONFIGS } from '@/types/meme';

// Rate limiting configuration
const RATE_LIMIT = {
  requests: 60, // requests per minute
  window: 60 * 1000, // 1 minute in milliseconds
};

class RateLimiter {
  private requests: number[] = [];

  canMakeRequest(): boolean {
    const now = Date.now();
    // Remove requests older than the window
    this.requests = this.requests.filter(time => now - time < RATE_LIMIT.window);
    
    if (this.requests.length >= RATE_LIMIT.requests) {
      return false;
    }
    
    this.requests.push(now);
    return true;
  }

  getTimeUntilNextRequest(): number {
    if (this.requests.length < RATE_LIMIT.requests) {
      return 0;
    }
    
    const oldestRequest = Math.min(...this.requests);
    return RATE_LIMIT.window - (Date.now() - oldestRequest);
  }
}

class RedditAPIService {
  private rateLimiter = new RateLimiter();
  private baseUrl = 'https://www.reddit.com';
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  private async makeRequest(url: string): Promise<any> {
    // Check rate limit
    if (!this.rateLimiter.canMakeRequest()) {
      const waitTime = this.rateLimiter.getTimeUntilNextRequest();
      throw new Error(`Rate limit exceeded. Try again in ${Math.ceil(waitTime / 1000)} seconds.`);
    }

    // Check cache
    const cached = this.cache.get(url);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'MemeForge/1.0.0 (Web App)',
        },
      });

      if (!response.ok) {
        throw new Error(`Reddit API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // Cache the response
      this.cache.set(url, { data, timestamp: Date.now() });
      
      return data;
    } catch (error) {
      console.error('Reddit API request failed:', error);
      throw error;
    }
  }

  async getTrendingMemes(subreddit: string = 'all', limit: number = 25): Promise<Meme[]> {
    try {
      const url = `${this.baseUrl}/r/${subreddit}/hot.json?limit=${limit}`;
      const response = await this.makeRequest(url);
      
      if (!response.data || !response.data.children) {
        throw new Error('Invalid Reddit API response');
      }

      const posts: RedditPostData[] = response.data.children.map((child: any) => child.data);
      
      return posts
        .filter(post => this.isValidMemePost(post))
        .map(post => this.convertToMeme(post))
        .sort((a, b) => b.appPotential - a.appPotential);
    } catch (error) {
      console.error('Failed to fetch trending memes:', error);
      // Return mock data as fallback
      return this.getMockMemes();
    }
  }

  async getSubredditMemes(subredditName: string, sortBy: 'hot' | 'new' | 'top' = 'hot', limit: number = 25): Promise<Meme[]> {
    try {
      const url = `${this.baseUrl}/r/${subredditName}/${sortBy}.json?limit=${limit}`;
      const response = await this.makeRequest(url);
      
      if (!response.data || !response.data.children) {
        throw new Error('Invalid Reddit API response');
      }

      const posts: RedditPostData[] = response.data.children.map((child: any) => child.data);
      
      return posts
        .filter(post => this.isValidMemePost(post))
        .map(post => this.convertToMeme(post));
    } catch (error) {
      console.error(`Failed to fetch memes from r/${subredditName}:`, error);
      return [];
    }
  }

  async searchMemes(query: string, limit: number = 25): Promise<Meme[]> {
    try {
      const encodedQuery = encodeURIComponent(query);
      const url = `${this.baseUrl}/search.json?q=${encodedQuery}&type=link&limit=${limit}`;
      const response = await this.makeRequest(url);
      
      if (!response.data || !response.data.children) {
        throw new Error('Invalid Reddit API response');
      }

      const posts: RedditPostData[] = response.data.children.map((child: any) => child.data);
      
      return posts
        .filter(post => this.isValidMemePost(post))
        .map(post => this.convertToMeme(post));
    } catch (error) {
      console.error('Failed to search memes:', error);
      return [];
    }
  }

  private isValidMemePost(post: RedditPostData): boolean {
    // Filter out text posts, videos, and non-image content
    if (post.selftext && post.selftext.length > 0) return false;
    if (!post.url) return false;
    
    // Check if it's an image URL or has preview images
    const isImage = /\.(jpg|jpeg|png|gif|webp)$/i.test(post.url) || 
                   post.url.includes('i.redd.it') || 
                   post.url.includes('imgur.com') ||
                   (post.preview && post.preview.images && post.preview.images.length > 0);
    
    if (!isImage) return false;
    
    // Filter out NSFW content
    if (post.over_18) return false;
    
    // Must have minimum engagement
    if (post.ups < 10) return false;
    
    return true;
  }

  private convertToMeme(post: RedditPostData): Meme {
    const subredditConfig = SUBREDDIT_CONFIGS.find(config => 
      config.name.toLowerCase() === post.subreddit.toLowerCase()
    );
    
    // Calculate app potential based on subreddit, engagement, and content
    const baseScore = subredditConfig ? subredditConfig.weight * 100 : 50;
    const engagementBonus = Math.min((post.ups / 1000) * 5, 20);
    const commentBonus = Math.min((post.num_comments / 50) * 5, 10);
    
    const appPotential = Math.min(Math.round(baseScore + engagementBonus + commentBonus), 100);
    
    // Get the best image URL
    let imageUrl = post.url;
    if (post.preview && post.preview.images && post.preview.images.length > 0) {
      imageUrl = post.preview.images[0].source.url.replace(/&amp;/g, '&');
    }
    
    return {
      id: post.id,
      title: post.title,
      imageUrl,
      subreddit: post.subreddit,
      upvotes: post.ups,
      comments: post.num_comments,
      trending: post.ups > 1000 && (Date.now() / 1000 - post.created_utc) < 86400, // Trending if >1k upvotes and <24h old
      appPotential,
      author: post.author,
      createdAt: new Date(post.created_utc * 1000),
      tags: this.extractTags(post.title, post.subreddit),
      nsfw: post.over_18,
    };
  }

  private extractTags(title: string, subreddit: string): string[] {
    const tags: string[] = [subreddit];
    
    // Extract common programming/tech terms
    const techTerms = [
      'javascript', 'python', 'react', 'css', 'html', 'node', 'api', 'database',
      'frontend', 'backend', 'fullstack', 'mobile', 'web', 'app', 'code', 'bug',
      'debug', 'deploy', 'git', 'github', 'stackoverflow', 'junior', 'senior'
    ];
    
    const titleLower = title.toLowerCase();
    techTerms.forEach(term => {
      if (titleLower.includes(term)) {
        tags.push(term);
      }
    });
    
    return [...new Set(tags)]; // Remove duplicates
  }

  private getMockMemes(): Meme[] {
    return [
      {
        id: 'mock1',
        title: 'When you fix a bug but create 3 more',
        imageUrl: 'https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400',
        subreddit: 'ProgrammerHumor',
        upvotes: 15420,
        comments: 312,
        trending: true,
        appPotential: 85,
        author: 'mockuser1',
        createdAt: new Date(),
        tags: ['ProgrammerHumor', 'bug', 'debug'],
      },
      {
        id: 'mock2',
        title: 'Me explaining my code to rubber duck',
        imageUrl: 'https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=400',
        subreddit: 'webdev',
        upvotes: 8934,
        comments: 156,
        trending: true,
        appPotential: 78,
        author: 'mockuser2',
        createdAt: new Date(),
        tags: ['webdev', 'debug', 'code'],
      },
      {
        id: 'mock3',
        title: 'CSS centering div in 2024',
        imageUrl: 'https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400',
        subreddit: 'css',
        upvotes: 12567,
        comments: 89,
        trending: false,
        appPotential: 92,
        author: 'mockuser3',
        createdAt: new Date(),
        tags: ['css', 'frontend', 'web'],
      },
    ];
  }

  // Get rate limit status
  getRateLimitStatus() {
    return {
      requestsRemaining: Math.max(0, RATE_LIMIT.requests - this.rateLimiter['requests'].length),
      resetTime: this.rateLimiter.getTimeUntilNextRequest(),
    };
  }

  // Clear cache manually
  clearCache() {
    this.cache.clear();
  }
}

export const redditAPI = new RedditAPIService();
export default redditAPI;
