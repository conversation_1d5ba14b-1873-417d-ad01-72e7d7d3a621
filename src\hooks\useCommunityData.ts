import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { communityService } from '@/services/communityService';
import { 
  User, 
  Vote, 
  Comment, 
  CommunityChallenge, 
  Leaderboard, 
  CommunityEvent,
  UserStats 
} from '@/types/community';

export const useCommunityData = (userId?: string) => {
  const queryClient = useQueryClient();
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  // User management
  const useUser = (targetUserId: string) => {
    return useQuery({
      queryKey: ['user', targetUserId],
      queryFn: () => communityService.getUser(targetUserId),
      enabled: !!targetUserId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  const createUser = useMutation({
    mutationFn: (userData: Partial<User>) => communityService.createUser(userData),
    onSuccess: (user) => {
      setCurrentUser(user);
      queryClient.setQueryData(['user', user.id], user);
    },
  });

  const updateUserStats = useMutation({
    mutationFn: ({ userId, stats }: { userId: string; stats: Partial<UserStats> }) =>
      communityService.updateUserStats(userId, stats),
    onSuccess: (user) => {
      if (user) {
        queryClient.setQueryData(['user', user.id], user);
        if (user.id === currentUser?.id) {
          setCurrentUser(user);
        }
      }
    },
  });

  // Voting system
  const submitVote = useMutation({
    mutationFn: ({ 
      userId, 
      targetId, 
      targetType, 
      voteType 
    }: { 
      userId: string; 
      targetId: string; 
      targetType: 'meme' | 'app' | 'comment'; 
      voteType: 'up' | 'down' 
    }) => communityService.submitVote(userId, targetId, targetType, voteType),
    onSuccess: (vote) => {
      // Invalidate votes for the target
      queryClient.invalidateQueries({ queryKey: ['votes', vote.targetId] });
      // Update user stats
      if (userId) {
        updateUserStats.mutate({ 
          userId, 
          stats: { communityVotes: (currentUser?.stats.communityVotes || 0) + 1 }
        });
      }
    },
  });

  const useVotes = (targetId: string) => {
    return useQuery({
      queryKey: ['votes', targetId],
      queryFn: () => communityService.getVotes(targetId),
      enabled: !!targetId,
      staleTime: 30 * 1000, // 30 seconds
    });
  };

  // Comment system
  const addComment = useMutation({
    mutationFn: ({ 
      userId, 
      targetId, 
      targetType, 
      content, 
      parentCommentId 
    }: { 
      userId: string; 
      targetId: string; 
      targetType: 'meme' | 'app'; 
      content: string; 
      parentCommentId?: string 
    }) => communityService.addComment(userId, targetId, targetType, content, parentCommentId),
    onSuccess: (comment) => {
      // Invalidate comments for the target
      queryClient.invalidateQueries({ queryKey: ['comments', comment.targetId] });
    },
  });

  const useComments = (targetId: string) => {
    return useQuery({
      queryKey: ['comments', targetId],
      queryFn: () => communityService.getComments(targetId),
      enabled: !!targetId,
      staleTime: 2 * 60 * 1000, // 2 minutes
    });
  };

  // Leaderboard system
  const useLeaderboard = (
    period: 'daily' | 'weekly' | 'monthly' | 'all-time' = 'all-time',
    category: 'apps-generated' | 'downloads' | 'community-votes' | 'reputation' = 'reputation'
  ) => {
    return useQuery({
      queryKey: ['leaderboard', period, category],
      queryFn: () => communityService.getLeaderboard(period, category),
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  // Challenge system
  const useChallenges = () => {
    return useQuery({
      queryKey: ['challenges'],
      queryFn: () => communityService.getChallenges(),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  const useActiveChallenge = () => {
    return useQuery({
      queryKey: ['active-challenge'],
      queryFn: () => communityService.getActiveChallenge(),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  const joinChallenge = useMutation({
    mutationFn: ({ challengeId, userId }: { challengeId: string; userId: string }) =>
      communityService.joinChallenge(challengeId, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['challenges'] });
      queryClient.invalidateQueries({ queryKey: ['active-challenge'] });
    },
  });

  // Event system
  const useUpcomingEvents = () => {
    return useQuery({
      queryKey: ['upcoming-events'],
      queryFn: () => communityService.getUpcomingEvents(),
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  const registerForEvent = useMutation({
    mutationFn: ({ eventId, userId }: { eventId: string; userId: string }) =>
      communityService.registerForEvent(eventId, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['upcoming-events'] });
    },
  });

  // Helper functions and computed values
  const getUserRank = (userId: string, leaderboard?: Leaderboard) => {
    if (!leaderboard) return null;
    const entry = leaderboard.entries.find(entry => entry.userId === userId);
    return entry?.rank || null;
  };

  const getUserBadgeInfo = (stats: UserStats, reputation: number) => {
    if (reputation >= 1000) {
      return { title: 'Meme Legend', color: '#F59E0B', icon: '👑', requirements: '1000+ reputation' };
    } else if (reputation >= 500) {
      return { title: 'App Master', color: '#8B5CF6', icon: '🚀', requirements: '500+ reputation' };
    } else if (reputation >= 100) {
      return { title: 'Creator', color: '#10B981', icon: '⭐', requirements: '100+ reputation' };
    } else if (stats.appsGenerated >= 5) {
      return { title: 'Builder', color: '#3B82F6', icon: '🔨', requirements: '5+ apps generated' };
    } else {
      return { title: 'Newcomer', color: '#6B7280', icon: '🌱', requirements: 'Join the community' };
    }
  };

  const getCommunityStats = () => {
    const leaderboardData = queryClient.getQueryData(['leaderboard', 'all-time', 'reputation']) as Leaderboard;
    const challenges = queryClient.getQueryData(['challenges']) as CommunityChallenge[];
    const events = queryClient.getQueryData(['upcoming-events']) as CommunityEvent[];

    return {
      totalUsers: leaderboardData?.entries.length || 0,
      activeChallenges: challenges?.filter(c => c.status === 'active').length || 0,
      upcomingEvents: events?.length || 0,
      topCreator: leaderboardData?.entries[0]?.username || 'Unknown',
    };
  };

  const getEngagementMetrics = (targetId: string) => {
    const votes = queryClient.getQueryData(['votes', targetId]) as { upvotes: number; downvotes: number } | undefined;
    const comments = queryClient.getQueryData(['comments', targetId]) as Comment[] | undefined;

    return {
      totalVotes: (votes?.upvotes || 0) + (votes?.downvotes || 0),
      voteRatio: votes ? votes.upvotes / Math.max(1, votes.upvotes + votes.downvotes) : 0,
      totalComments: comments?.length || 0,
      engagementScore: ((votes?.upvotes || 0) * 2 + (comments?.length || 0) * 3),
    };
  };

  const isUserActive = (user: User) => {
    const daysSinceJoin = Math.floor((Date.now() - user.joinDate.getTime()) / (1000 * 60 * 60 * 24));
    const activityScore = user.stats.memesAnalyzed + user.stats.appsGenerated + user.stats.communityVotes;
    
    return daysSinceJoin < 30 || activityScore > 10;
  };

  const getRecommendedActions = (user: User) => {
    const recommendations: string[] = [];
    
    if (user.stats.memesAnalyzed < 5) {
      recommendations.push('Analyze more trending memes to discover app opportunities');
    }
    
    if (user.stats.appsGenerated === 0) {
      recommendations.push('Generate your first app concept from a meme');
    }
    
    if (user.stats.communityVotes < 10) {
      recommendations.push('Engage with the community by voting on apps and memes');
    }
    
    if (user.reputation < 50) {
      recommendations.push('Participate in challenges to boost your reputation');
    }

    return recommendations;
  };

  const getRateLimitStatus = () => communityService.getRateLimitStatus();

  return {
    // User management
    useUser,
    createUser,
    updateUserStats,
    currentUser,
    setCurrentUser,

    // Voting
    submitVote,
    useVotes,

    // Comments
    addComment,
    useComments,

    // Leaderboards
    useLeaderboard,

    // Challenges
    useChallenges,
    useActiveChallenge,
    joinChallenge,

    // Events
    useUpcomingEvents,
    registerForEvent,

    // Helper functions
    getUserRank,
    getUserBadgeInfo,
    getCommunityStats,
    getEngagementMetrics,
    isUserActive,
    getRecommendedActions,
    getRateLimitStatus,
  };
};

export default useCommunityData;
