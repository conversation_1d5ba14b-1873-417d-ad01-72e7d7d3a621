
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, ThumbsUp, MessageCircle, Brain, Zap } from 'lucide-react';

interface Meme {
  id: string;
  title: string;
  imageUrl: string;
  subreddit: string;
  upvotes: number;
  comments: number;
  trending: boolean;
  appPotential: number;
}

interface MemeCardProps {
  meme: Meme;
  onAnalyze: (meme: Meme) => void;
}

const MemeCard = ({ meme, onAnalyze }: MemeCardProps) => {
  const getPotentialColor = (potential: number) => {
    if (potential >= 90) return 'text-green-400 border-green-500/50';
    if (potential >= 80) return 'text-yellow-400 border-yellow-500/50';
    return 'text-red-400 border-red-500/50';
  };

  return (
    <Card className="bg-black/30 border-purple-800/30 backdrop-blur-sm hover:bg-black/40 hover:border-purple-600/50 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/20">
      <div className="relative">
        <img 
          src={meme.imageUrl} 
          alt={meme.title}
          className="w-full h-48 object-cover rounded-t-lg"
        />
        {meme.trending && (
          <Badge className="absolute top-2 right-2 bg-orange-600/90 text-white">
            <TrendingUp className="h-3 w-3 mr-1" />
            Trending
          </Badge>
        )}
      </div>
      
      <CardHeader className="pb-2">
        <CardTitle className="text-white text-lg line-clamp-2">{meme.title}</CardTitle>
        <CardDescription className="flex items-center justify-between">
          <span className="text-purple-300">r/{meme.subreddit}</span>
          <Badge className={`${getPotentialColor(meme.appPotential)} bg-transparent`}>
            {meme.appPotential}% potential
          </Badge>
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between text-sm text-slate-400">
          <div className="flex items-center gap-1">
            <ThumbsUp className="h-4 w-4" />
            <span>{meme.upvotes.toLocaleString()}</span>
          </div>
          <div className="flex items-center gap-1">
            <MessageCircle className="h-4 w-4" />
            <span>{meme.comments}</span>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button 
            onClick={() => onAnalyze(meme)}
            className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
            size="sm"
          >
            <Brain className="h-4 w-4 mr-1" />
            Analyze
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            className="border-purple-500/50 text-purple-300 hover:bg-purple-900/30"
          >
            <Zap className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default MemeCard;
