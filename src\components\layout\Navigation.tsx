import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  TrendingUp, 
  Brain, 
  Users, 
  Trophy, 
  Code, 
  MessageCircle,
  Calendar,
  Star,
  Zap,
  ChevronDown,
  ChevronRight,
  Home,
  Search,
  Settings,
  HelpCircle
} from 'lucide-react';

interface NavigationProps {
  activeSection?: string;
  onSectionChange?: (section: string) => void;
  isCollapsed?: boolean;
  className?: string;
}

interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  badge?: string | number;
  children?: NavItem[];
}

const Navigation = ({ 
  activeSection = 'discover', 
  onSectionChange, 
  isCollapsed = false,
  className 
}: NavigationProps) => {
  const [expandedSections, setExpandedSections] = useState<string[]>(['main']);

  const navItems: NavItem[] = [
    {
      id: 'main',
      label: 'Main',
      icon: <Home className="h-4 w-4" />,
      children: [
        {
          id: 'discover',
          label: 'Discover',
          icon: <TrendingUp className="h-4 w-4" />,
          badge: 'Hot'
        },
        {
          id: 'generate',
          label: 'Generate',
          icon: <Brain className="h-4 w-4" />
        },
        {
          id: 'search',
          label: 'Search',
          icon: <Search className="h-4 w-4" />
        }
      ]
    },
    {
      id: 'community',
      label: 'Community',
      icon: <Users className="h-4 w-4" />,
      children: [
        {
          id: 'discussions',
          label: 'Discussions',
          icon: <MessageCircle className="h-4 w-4" />,
          badge: 12
        },
        {
          id: 'leaderboard',
          label: 'Leaderboard',
          icon: <Trophy className="h-4 w-4" />
        },
        {
          id: 'events',
          label: 'Events',
          icon: <Calendar className="h-4 w-4" />,
          badge: 'New'
        }
      ]
    },
    {
      id: 'showcase',
      label: 'Showcase',
      icon: <Star className="h-4 w-4" />,
      children: [
        {
          id: 'hall-of-fame',
          label: 'Hall of Fame',
          icon: <Trophy className="h-4 w-4" />
        },
        {
          id: 'featured-apps',
          label: 'Featured Apps',
          icon: <Zap className="h-4 w-4" />
        },
        {
          id: 'success-stories',
          label: 'Success Stories',
          icon: <Star className="h-4 w-4" />
        }
      ]
    },
    {
      id: 'tools',
      label: 'Developer Tools',
      icon: <Code className="h-4 w-4" />,
      children: [
        {
          id: 'code-templates',
          label: 'Code Templates',
          icon: <Code className="h-4 w-4" />
        },
        {
          id: 'api-docs',
          label: 'API Documentation',
          icon: <HelpCircle className="h-4 w-4" />
        }
      ]
    }
  ];

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const handleItemClick = (itemId: string) => {
    onSectionChange?.(itemId);
  };

  const renderNavItem = (item: NavItem, level: number = 0) => {
    const isExpanded = expandedSections.includes(item.id);
    const isActive = activeSection === item.id;
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.id} className="space-y-1">
        <Button
          variant={isActive ? "secondary" : "ghost"}
          className={`w-full justify-start h-auto p-2 ${
            level > 0 ? 'ml-4' : ''
          } ${
            isActive 
              ? 'bg-purple-600/30 text-purple-300 hover:bg-purple-600/40' 
              : 'text-slate-300 hover:text-white hover:bg-slate-800/50'
          }`}
          onClick={() => {
            if (hasChildren) {
              toggleSection(item.id);
            } else {
              handleItemClick(item.id);
            }
          }}
        >
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              {item.icon}
              {!isCollapsed && (
                <>
                  <span className="text-sm font-medium">{item.label}</span>
                  {item.badge && (
                    <Badge 
                      className={`text-xs h-5 px-1.5 ${
                        typeof item.badge === 'string' 
                          ? 'bg-red-600/20 text-red-300 border-red-500/30'
                          : 'bg-blue-600/20 text-blue-300 border-blue-500/30'
                      }`}
                    >
                      {item.badge}
                    </Badge>
                  )}
                </>
              )}
            </div>
            {hasChildren && !isCollapsed && (
              <div className="ml-auto">
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </div>
            )}
          </div>
        </Button>

        {hasChildren && isExpanded && !isCollapsed && (
          <div className="space-y-1">
            {item.children!.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (isCollapsed) {
    return (
      <div className={`space-y-2 ${className}`}>
        {navItems.map(section => (
          <div key={section.id} className="space-y-1">
            {section.children ? (
              section.children.map(item => (
                <Button
                  key={item.id}
                  variant={activeSection === item.id ? "secondary" : "ghost"}
                  size="sm"
                  className={`w-full justify-center p-2 ${
                    activeSection === item.id
                      ? 'bg-purple-600/30 text-purple-300'
                      : 'text-slate-400 hover:text-white'
                  }`}
                  onClick={() => handleItemClick(item.id)}
                  title={item.label}
                >
                  {item.icon}
                </Button>
              ))
            ) : (
              <Button
                variant={activeSection === section.id ? "secondary" : "ghost"}
                size="sm"
                className={`w-full justify-center p-2 ${
                  activeSection === section.id
                    ? 'bg-purple-600/30 text-purple-300'
                    : 'text-slate-400 hover:text-white'
                }`}
                onClick={() => handleItemClick(section.id)}
                title={section.label}
              >
                {section.icon}
              </Button>
            )}
          </div>
        ))}
      </div>
    );
  }

  return (
    <Card className={`bg-black/30 border-slate-700/50 backdrop-blur-sm ${className}`}>
      <CardContent className="p-4 space-y-4">
        {navItems.map((section, index) => (
          <div key={section.id}>
            {renderNavItem(section)}
            {index < navItems.length - 1 && (
              <Separator className="bg-slate-700 my-3" />
            )}
          </div>
        ))}

        <Separator className="bg-slate-700" />

        {/* Quick Stats */}
        <div className="space-y-2">
          <h4 className="text-xs font-semibold text-slate-400 uppercase tracking-wider">
            Quick Stats
          </h4>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between text-slate-300">
              <span>Memes Analyzed</span>
              <span className="text-green-400">1,337</span>
            </div>
            <div className="flex justify-between text-slate-300">
              <span>Apps Generated</span>
              <span className="text-blue-400">42,069</span>
            </div>
            <div className="flex justify-between text-slate-300">
              <span>Active Users</span>
              <span className="text-purple-400">∞</span>
            </div>
          </div>
        </div>

        <Separator className="bg-slate-700" />

        {/* Settings */}
        <Button
          variant="ghost"
          className="w-full justify-start text-slate-400 hover:text-white"
          onClick={() => handleItemClick('settings')}
        >
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </Button>
      </CardContent>
    </Card>
  );
};

export default Navigation;
