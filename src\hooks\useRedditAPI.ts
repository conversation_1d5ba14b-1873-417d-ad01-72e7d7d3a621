
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { redditAPI } from '@/services/redditAPI';
import { Meme } from '@/types/meme';

export const useRedditAPI = (subreddit: string = 'all') => {
  const queryClient = useQueryClient();

  // Fetch trending memes
  const {
    data: memes = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['reddit-memes', subreddit],
    queryFn: () => redditAPI.getTrendingMemes(subreddit),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Fetch specific subreddit memes
  const useSubredditMemes = (subredditName: string, sortBy: 'hot' | 'new' | 'top' = 'hot') => {
    return useQuery({
      queryKey: ['subreddit-memes', subredditName, sortBy],
      queryFn: () => redditAPI.getSubredditMemes(subredditName, sortBy),
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
      enabled: !!subredditName,
    });
  };

  // Search memes
  const searchMemes = useMutation({
    mutationFn: (query: string) => redditAPI.searchMemes(query),
    onSuccess: (data) => {
      // Cache search results
      queryClient.setQueryData(['search-memes'], data);
    },
  });

  // Helper functions
  const getTrendingMemes = () => memes.filter(meme => meme.trending);

  const getHighPotentialMemes = () => memes.filter(meme => meme.appPotential >= 80);

  const getMemesBySubreddit = (targetSubreddit: string) =>
    memes.filter(meme => meme.subreddit.toLowerCase() === targetSubreddit.toLowerCase());

  const getRateLimitStatus = () => redditAPI.getRateLimitStatus();

  const clearCache = () => {
    redditAPI.clearCache();
    queryClient.invalidateQueries({ queryKey: ['reddit-memes'] });
  };

  return {
    memes,
    isLoading,
    error,
    refetch,
    getTrendingMemes,
    getHighPotentialMemes,
    getMemesBySubreddit,
    useSubredditMemes,
    searchMemes,
    getRateLimitStatus,
    clearCache,
  };
};
