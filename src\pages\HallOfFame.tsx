import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Crown, 
  Trophy, 
  Star, 
  TrendingUp, 
  Download,
  ExternalLink,
  Calendar,
  Users,
  Zap,
  Award
} from 'lucide-react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const HallOfFame = () => {
  const [activeTab, setActiveTab] = useState('featured');

  // Mock data for featured apps
  const featuredApps = [
    {
      id: '1',
      name: 'TaskMaster Pro',
      description: 'A productivity app born from the "This is Fine" meme that helps you manage chaos',
      originalMeme: 'This is Fine Dog',
      creator: 'CodeWizard42',
      downloads: 50000,
      rating: 4.8,
      revenue: '$25,000',
      launchDate: '2024-01-15',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400',
      tags: ['Productivity', 'Task Management', 'React'],
      status: 'Live'
    },
    {
      id: '2',
      name: 'DebugBuddy',
      description: 'Debugging assistant inspired by "Stack Overflow Copy Paste" memes',
      originalMeme: 'Stack Overflow Copy Paste',
      creator: 'DevMaster',
      downloads: 35000,
      rating: 4.6,
      revenue: '$18,500',
      launchDate: '2024-02-20',
      image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400',
      tags: ['Developer Tools', 'Debugging', 'VS Code'],
      status: 'Live'
    },
    {
      id: '3',
      name: 'MoodSync',
      description: 'Social mood tracking app from "How are you feeling?" meme template',
      originalMeme: 'Drake Pointing Meme',
      creator: 'UXQueen',
      downloads: 75000,
      rating: 4.9,
      revenue: '$42,000',
      launchDate: '2024-03-10',
      image: 'https://images.unsplash.com/photo-1512486130939-2c4f79935e4f?w=400',
      tags: ['Social', 'Health', 'React Native'],
      status: 'Live'
    }
  ];

  const topCreators = [
    {
      id: '1',
      username: 'CodeWizard42',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
      appsCreated: 12,
      totalDownloads: 250000,
      totalRevenue: '$125,000',
      badge: { title: 'Meme Legend', icon: '👑', color: '#F59E0B' },
      joinDate: '2023-06-15'
    },
    {
      id: '2',
      username: 'UXQueen',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
      appsCreated: 8,
      totalDownloads: 180000,
      totalRevenue: '$95,000',
      badge: { title: 'App Master', icon: '🚀', color: '#8B5CF6' },
      joinDate: '2023-08-20'
    },
    {
      id: '3',
      username: 'DevMaster',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
      appsCreated: 15,
      totalDownloads: 320000,
      totalRevenue: '$160,000',
      badge: { title: 'Creator', icon: '⭐', color: '#10B981' },
      joinDate: '2023-05-10'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Crown className="h-12 w-12 text-yellow-400" />
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-yellow-400 via-orange-400 to-red-600 bg-clip-text text-transparent">
              Hall of Fame
            </h1>
            <Crown className="h-12 w-12 text-yellow-400" />
          </div>
          <p className="text-xl text-slate-300 mb-6 max-w-2xl mx-auto">
            Celebrating the most successful meme-to-app transformations and the creators who made them reality
          </p>
        </div>

        {/* Success Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="bg-gradient-to-r from-yellow-900/50 to-orange-900/50 border-yellow-500/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <Trophy className="h-8 w-8 mx-auto mb-2 text-yellow-400" />
              <div className="text-2xl font-bold text-white">127</div>
              <div className="text-yellow-200 text-sm">Successful Apps</div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-green-900/50 to-emerald-900/50 border-green-500/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <Download className="h-8 w-8 mx-auto mb-2 text-green-400" />
              <div className="text-2xl font-bold text-white">2.5M+</div>
              <div className="text-green-200 text-sm">Total Downloads</div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 border-purple-500/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <Users className="h-8 w-8 mx-auto mb-2 text-purple-400" />
              <div className="text-2xl font-bold text-white">1,337</div>
              <div className="text-purple-200 text-sm">Active Creators</div>
            </CardContent>
          </Card>
          
          <Card className="bg-gradient-to-r from-blue-900/50 to-cyan-900/50 border-blue-500/50 backdrop-blur-sm">
            <CardContent className="p-4 text-center">
              <Zap className="h-8 w-8 mx-auto mb-2 text-blue-400" />
              <div className="text-2xl font-bold text-white">$1.2M+</div>
              <div className="text-blue-200 text-sm">Revenue Generated</div>
            </CardContent>
          </Card>
        </div>

        {/* Hall of Fame Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-black/30 border border-slate-700 mb-8">
            <TabsTrigger value="featured" className="data-[state=active]:bg-yellow-600/30">
              <Star className="h-4 w-4 mr-2" />
              Featured Apps
            </TabsTrigger>
            <TabsTrigger value="creators" className="data-[state=active]:bg-yellow-600/30">
              <Crown className="h-4 w-4 mr-2" />
              Top Creators
            </TabsTrigger>
            <TabsTrigger value="trending" className="data-[state=active]:bg-yellow-600/30">
              <TrendingUp className="h-4 w-4 mr-2" />
              Trending Now
            </TabsTrigger>
          </TabsList>

          {/* Featured Apps Tab */}
          <TabsContent value="featured" className="space-y-6">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredApps.map((app, index) => (
                <Card key={app.id} className="bg-black/30 border-slate-700/50 backdrop-blur-sm hover:border-yellow-500/50 transition-all duration-300">
                  <CardHeader className="relative">
                    {index === 0 && (
                      <div className="absolute -top-2 -right-2">
                        <Badge className="bg-yellow-600 text-white border-yellow-500">
                          <Crown className="h-3 w-3 mr-1" />
                          #1 App
                        </Badge>
                      </div>
                    )}
                    <img 
                      src={app.image} 
                      alt={app.name}
                      className="w-full h-48 object-cover rounded-lg mb-4"
                    />
                    <CardTitle className="text-white">{app.name}</CardTitle>
                    <CardDescription className="text-slate-300">
                      {app.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center gap-2">
                      <span className="text-slate-400 text-sm">From meme:</span>
                      <Badge className="bg-purple-600/20 text-purple-300 border-purple-500/30 text-xs">
                        {app.originalMeme}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-slate-400">Downloads:</span>
                        <div className="text-green-400 font-semibold">{app.downloads.toLocaleString()}</div>
                      </div>
                      <div>
                        <span className="text-slate-400">Revenue:</span>
                        <div className="text-yellow-400 font-semibold">{app.revenue}</div>
                      </div>
                      <div>
                        <span className="text-slate-400">Rating:</span>
                        <div className="text-blue-400 font-semibold flex items-center gap-1">
                          <Star className="h-3 w-3 fill-current" />
                          {app.rating}
                        </div>
                      </div>
                      <div>
                        <span className="text-slate-400">Creator:</span>
                        <div className="text-purple-400 font-semibold">{app.creator}</div>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-1">
                      {app.tags.map((tag, tagIndex) => (
                        <Badge key={tagIndex} className="text-xs bg-slate-700/50 text-slate-300">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex gap-2">
                      <Button size="sm" className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                        <ExternalLink className="h-3 w-3 mr-1" />
                        View App
                      </Button>
                      <Button size="sm" variant="outline" className="border-slate-600 text-slate-300">
                        Case Study
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Top Creators Tab */}
          <TabsContent value="creators" className="space-y-6">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Crown className="h-6 w-6 text-yellow-400" />
                  Hall of Fame Creators
                </CardTitle>
                <CardDescription className="text-slate-300">
                  The most successful meme-to-app creators in our community
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {topCreators.map((creator, index) => (
                    <div key={creator.id} className="flex items-center gap-6 p-6 bg-slate-800/30 rounded-lg hover:bg-slate-800/50 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${
                          index === 0 ? 'bg-yellow-600 text-white' :
                          index === 1 ? 'bg-gray-400 text-white' :
                          index === 2 ? 'bg-amber-600 text-white' :
                          'bg-slate-600 text-slate-300'
                        }`}>
                          {index + 1}
                        </div>
                        <Avatar className="h-16 w-16">
                          <AvatarImage src={creator.avatar} alt={creator.username} />
                          <AvatarFallback>{creator.username.charAt(0).toUpperCase()}</AvatarFallback>
                        </Avatar>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-bold text-white">{creator.username}</h3>
                          <Badge 
                            className="text-sm"
                            style={{ 
                              backgroundColor: `${creator.badge.color}20`, 
                              color: creator.badge.color,
                              borderColor: `${creator.badge.color}50`
                            }}
                          >
                            {creator.badge.icon} {creator.badge.title}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-slate-400">Apps Created:</span>
                            <div className="text-blue-400 font-semibold">{creator.appsCreated}</div>
                          </div>
                          <div>
                            <span className="text-slate-400">Total Downloads:</span>
                            <div className="text-green-400 font-semibold">{creator.totalDownloads.toLocaleString()}</div>
                          </div>
                          <div>
                            <span className="text-slate-400">Revenue Generated:</span>
                            <div className="text-yellow-400 font-semibold">{creator.totalRevenue}</div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <Button variant="outline" className="border-slate-600 text-slate-300">
                          View Profile
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Trending Now Tab */}
          <TabsContent value="trending" className="space-y-6">
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white">Trending This Week</CardTitle>
                <CardDescription className="text-slate-300">
                  The hottest new apps and rising creators
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <TrendingUp className="h-16 w-16 mx-auto mb-4 text-slate-400 opacity-50" />
                  <h3 className="text-xl font-semibold text-white mb-2">Trending Data Coming Soon</h3>
                  <p className="text-slate-400 mb-4">
                    We're building real-time trending analytics for the community
                  </p>
                  <Button className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
                    Get Early Access
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <Footer />
    </div>
  );
};

export default HallOfFame;
