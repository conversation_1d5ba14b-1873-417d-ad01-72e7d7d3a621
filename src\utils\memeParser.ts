import { RedditPostData, Meme, SUBREDDIT_CONFIGS } from '@/types/meme';

/**
 * Utility functions for parsing and processing meme data
 */

export class MemeParser {
  /**
   * Convert Reddit post data to Meme format
   */
  static parseRedditPost(post: RedditPostData): Meme | null {
    try {
      // Validate required fields
      if (!post.id || !post.title || !post.subreddit) {
        return null;
      }

      // Filter out invalid content
      if (!this.isValidMemePost(post)) {
        return null;
      }

      const subredditConfig = SUBREDDIT_CONFIGS.find(config => 
        config.name.toLowerCase() === post.subreddit.toLowerCase()
      );

      // Calculate app potential
      const appPotential = this.calculateAppPotential(post, subredditConfig);

      // Get the best image URL
      const imageUrl = this.extractImageUrl(post);

      // Extract tags from title and subreddit
      const tags = this.extractTags(post.title, post.subreddit);

      return {
        id: post.id,
        title: post.title,
        imageUrl,
        subreddit: post.subreddit,
        upvotes: post.ups,
        comments: post.num_comments,
        trending: this.isTrending(post),
        appPotential,
        author: post.author,
        createdAt: new Date(post.created_utc * 1000),
        tags,
        nsfw: post.over_18,
      };
    } catch (error) {
      console.error('Error parsing Reddit post:', error);
      return null;
    }
  }

  /**
   * Validate if a Reddit post is suitable for meme analysis
   */
  static isValidMemePost(post: RedditPostData): boolean {
    // Filter out text posts
    if (post.selftext && post.selftext.length > 0) return false;
    
    // Must have a URL
    if (!post.url) return false;
    
    // Check if it's an image URL or has preview images
    const isImage = this.isImageUrl(post.url) || this.hasPreviewImages(post);
    if (!isImage) return false;
    
    // Filter out NSFW content
    if (post.over_18) return false;
    
    // Must have minimum engagement
    if (post.ups < 10) return false;
    
    // Filter out deleted/removed posts
    if (post.author === '[deleted]' || post.title === '[removed]') return false;
    
    return true;
  }

  /**
   * Check if URL is an image
   */
  static isImageUrl(url: string): boolean {
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp)$/i;
    const imageHosts = ['i.redd.it', 'imgur.com', 'i.imgur.com'];
    
    return imageExtensions.test(url) || imageHosts.some(host => url.includes(host));
  }

  /**
   * Check if post has preview images
   */
  static hasPreviewImages(post: RedditPostData): boolean {
    return !!(post.preview && post.preview.images && post.preview.images.length > 0);
  }

  /**
   * Extract the best image URL from a Reddit post
   */
  static extractImageUrl(post: RedditPostData): string {
    // Try preview images first (usually higher quality)
    if (post.preview && post.preview.images && post.preview.images.length > 0) {
      const previewUrl = post.preview.images[0].source.url;
      return previewUrl.replace(/&amp;/g, '&');
    }
    
    // Fall back to the post URL
    return post.url;
  }

  /**
   * Calculate app potential based on various factors
   */
  static calculateAppPotential(post: RedditPostData, subredditConfig?: any): number {
    let potential = 0;
    
    // Base score from subreddit relevance (0-40 points)
    if (subredditConfig) {
      potential += subredditConfig.weight * 40;
    } else {
      potential += 20; // Default for unknown subreddits
    }
    
    // Engagement score (0-30 points)
    const engagementScore = Math.min(30, (post.ups / 1000) * 10 + (post.num_comments / 100) * 5);
    potential += engagementScore;
    
    // Title analysis (0-20 points)
    const titleScore = this.analyzeTitleForAppPotential(post.title);
    potential += titleScore;
    
    // Recency bonus (0-10 points)
    const recencyScore = this.calculateRecencyScore(post.created_utc);
    potential += recencyScore;
    
    return Math.min(100, Math.round(potential));
  }

  /**
   * Analyze title for app-related keywords and concepts
   */
  static analyzeTitleForAppPotential(title: string): number {
    const titleLower = title.toLowerCase();
    let score = 0;
    
    // High-value keywords (5 points each)
    const highValueKeywords = [
      'app', 'tool', 'website', 'platform', 'system', 'solution', 'fix', 'solve',
      'automate', 'optimize', 'improve', 'enhance', 'streamline'
    ];
    
    // Medium-value keywords (3 points each)
    const mediumValueKeywords = [
      'problem', 'issue', 'challenge', 'workflow', 'process', 'method',
      'hack', 'trick', 'tip', 'feature', 'function'
    ];
    
    // Tech-related keywords (2 points each)
    const techKeywords = [
      'code', 'programming', 'developer', 'software', 'api', 'database',
      'frontend', 'backend', 'ui', 'ux', 'design', 'interface'
    ];
    
    // Count keyword matches
    highValueKeywords.forEach(keyword => {
      if (titleLower.includes(keyword)) score += 5;
    });
    
    mediumValueKeywords.forEach(keyword => {
      if (titleLower.includes(keyword)) score += 3;
    });
    
    techKeywords.forEach(keyword => {
      if (titleLower.includes(keyword)) score += 2;
    });
    
    return Math.min(20, score);
  }

  /**
   * Calculate recency score (newer posts get higher scores)
   */
  static calculateRecencyScore(createdUtc: number): number {
    const now = Date.now() / 1000;
    const ageInHours = (now - createdUtc) / 3600;
    
    if (ageInHours < 6) return 10;
    if (ageInHours < 24) return 8;
    if (ageInHours < 72) return 5;
    if (ageInHours < 168) return 2; // 1 week
    return 0;
  }

  /**
   * Determine if a post is trending
   */
  static isTrending(post: RedditPostData): boolean {
    const ageInHours = (Date.now() / 1000 - post.created_utc) / 3600;
    const upvoteThreshold = 1000;
    const maxAge = 24; // 24 hours
    
    return post.ups > upvoteThreshold && ageInHours < maxAge;
  }

  /**
   * Extract relevant tags from title and subreddit
   */
  static extractTags(title: string, subreddit: string): string[] {
    const tags: string[] = [subreddit];
    const titleLower = title.toLowerCase();
    
    // Programming languages
    const programmingLanguages = [
      'javascript', 'python', 'java', 'typescript', 'react', 'vue', 'angular',
      'node', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin', 'dart'
    ];
    
    // Technologies
    const technologies = [
      'api', 'database', 'sql', 'nosql', 'mongodb', 'postgresql', 'mysql',
      'redis', 'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'firebase'
    ];
    
    // Concepts
    const concepts = [
      'frontend', 'backend', 'fullstack', 'mobile', 'web', 'desktop',
      'ui', 'ux', 'design', 'css', 'html', 'responsive', 'animation'
    ];
    
    // Development terms
    const devTerms = [
      'bug', 'debug', 'test', 'deploy', 'git', 'github', 'stackoverflow',
      'code', 'programming', 'developer', 'junior', 'senior', 'intern'
    ];
    
    const allTerms = [...programmingLanguages, ...technologies, ...concepts, ...devTerms];
    
    allTerms.forEach(term => {
      if (titleLower.includes(term)) {
        tags.push(term);
      }
    });
    
    return [...new Set(tags)]; // Remove duplicates
  }

  /**
   * Batch process multiple Reddit posts
   */
  static parseRedditPosts(posts: RedditPostData[]): Meme[] {
    return posts
      .map(post => this.parseRedditPost(post))
      .filter((meme): meme is Meme => meme !== null)
      .sort((a, b) => b.appPotential - a.appPotential);
  }

  /**
   * Filter memes by criteria
   */
  static filterMemes(memes: Meme[], criteria: {
    minAppPotential?: number;
    maxAge?: number; // in hours
    subreddits?: string[];
    trending?: boolean;
    tags?: string[];
  }): Meme[] {
    return memes.filter(meme => {
      // App potential filter
      if (criteria.minAppPotential && meme.appPotential < criteria.minAppPotential) {
        return false;
      }
      
      // Age filter
      if (criteria.maxAge) {
        const ageInHours = (Date.now() - meme.createdAt.getTime()) / (1000 * 60 * 60);
        if (ageInHours > criteria.maxAge) return false;
      }
      
      // Subreddit filter
      if (criteria.subreddits && !criteria.subreddits.includes(meme.subreddit)) {
        return false;
      }
      
      // Trending filter
      if (criteria.trending !== undefined && meme.trending !== criteria.trending) {
        return false;
      }
      
      // Tags filter
      if (criteria.tags && criteria.tags.length > 0) {
        const hasMatchingTag = criteria.tags.some(tag => 
          meme.tags?.some(memeTag => 
            memeTag.toLowerCase().includes(tag.toLowerCase())
          )
        );
        if (!hasMatchingTag) return false;
      }
      
      return true;
    });
  }

  /**
   * Get meme statistics
   */
  static getMemeStats(memes: Meme[]) {
    if (memes.length === 0) {
      return {
        total: 0,
        averageAppPotential: 0,
        trendingCount: 0,
        topSubreddits: [],
        mostCommonTags: [],
      };
    }

    const total = memes.length;
    const averageAppPotential = Math.round(
      memes.reduce((sum, meme) => sum + meme.appPotential, 0) / total
    );
    const trendingCount = memes.filter(meme => meme.trending).length;

    // Top subreddits
    const subredditCounts: Record<string, number> = {};
    memes.forEach(meme => {
      subredditCounts[meme.subreddit] = (subredditCounts[meme.subreddit] || 0) + 1;
    });
    const topSubreddits = Object.entries(subredditCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([subreddit, count]) => ({ subreddit, count }));

    // Most common tags
    const tagCounts: Record<string, number> = {};
    memes.forEach(meme => {
      meme.tags?.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    });
    const mostCommonTags = Object.entries(tagCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([tag, count]) => ({ tag, count }));

    return {
      total,
      averageAppPotential,
      trendingCount,
      topSubreddits,
      mostCommonTags,
    };
  }
}

export default MemeParser;
