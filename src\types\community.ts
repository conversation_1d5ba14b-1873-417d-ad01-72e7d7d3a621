
export interface User {
  id: string;
  username: string;
  email: string;
  avatar: string;
  joinDate: Date;
  reputation: number;
  badge: UserBadge;
  stats: UserStats;
  preferences: UserPreferences;
}

export interface UserBadge {
  title: string;
  color: string;
  icon: string;
  requirements: string;
}

export interface UserStats {
  memesAnalyzed: number;
  appsGenerated: number;
  communityVotes: number;
  successfulApps: number;
  totalDownloads: number;
}

export interface UserPreferences {
  favoriteSubreddits: string[];
  preferredTechStack: string[];
  notificationSettings: NotificationSettings;
  privacy: PrivacySettings;
}

export interface NotificationSettings {
  newTrendingMemes: boolean;
  analysisComplete: boolean;
  communityUpdates: boolean;
  appFeatured: boolean;
}

export interface PrivacySettings {
  profilePublic: boolean;
  showStats: boolean;
  allowMessaging: boolean;
}

export interface Vote {
  id: string;
  userId: string;
  targetId: string;
  targetType: 'meme' | 'app' | 'comment';
  voteType: 'up' | 'down';
  createdAt: Date;
}

export interface Comment {
  id: string;
  userId: string;
  username: string;
  avatar: string;
  targetId: string;
  targetType: 'meme' | 'app';
  content: string;
  parentCommentId?: string;
  upvotes: number;
  downvotes: number;
  createdAt: Date;
  updatedAt?: Date;
  replies: Comment[];
}

export interface CommunityChallenge {
  id: string;
  title: string;
  description: string;
  rules: string[];
  startDate: Date;
  endDate: Date;
  prizes: Prize[];
  participants: string[];
  submissions: ChallengeSubmission[];
  status: 'upcoming' | 'active' | 'voting' | 'completed';
}

export interface Prize {
  rank: number;
  title: string;
  description: string;
  value?: string;
}

export interface ChallengeSubmission {
  id: string;
  challengeId: string;
  userId: string;
  appId: string;
  submissionDate: Date;
  votes: number;
  description: string;
}

export interface Leaderboard {
  period: 'daily' | 'weekly' | 'monthly' | 'all-time';
  category: 'apps-generated' | 'downloads' | 'community-votes' | 'reputation';
  entries: LeaderboardEntry[];
  lastUpdated: Date;
}

export interface LeaderboardEntry {
  rank: number;
  userId: string;
  username: string;
  avatar: string;
  score: number;
  change: number; // Position change since last period
  badge: UserBadge;
}

export interface CommunityEvent {
  id: string;
  title: string;
  description: string;
  type: 'workshop' | 'hackathon' | 'showcase' | 'meetup';
  startTime: Date;
  endTime: Date;
  location: 'online' | 'offline';
  maxParticipants?: number;
  registeredParticipants: string[];
  organizer: string;
  tags: string[];
}
