
export interface GeneratedApp {
  id: string;
  name: string;
  description: string;
  originalMemeId: string;
  originalMemeTitle: string;
  creator: string;
  createdAt: Date;
  rating: number;
  downloads: number;
  imageUrl: string;
  tags: string[];
  featured: boolean;
  status: 'concept' | 'prototype' | 'launched' | 'archived';
  codeRepository?: string;
  liveDemo?: string;
  techStack: string[];
  features: AppFeature[];
  blueprint: AppBlueprint;
}

export interface AppFeature {
  id: string;
  name: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  implemented: boolean;
  estimatedHours: number;
}

export interface AppBlueprint {
  architecture: string;
  components: ComponentSpec[];
  dataModels: DataModel[];
  apiEndpoints: APIEndpoint[];
  userFlow: UserFlowStep[];
  mockups?: string[];
}

export interface ComponentSpec {
  name: string;
  type: 'page' | 'component' | 'utility';
  description: string;
  props?: string[];
  dependencies: string[];
}

export interface DataModel {
  name: string;
  fields: ModelField[];
  relationships: string[];
}

export interface ModelField {
  name: string;
  type: string;
  required: boolean;
  description: string;
}

export interface APIEndpoint {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  description: string;
  requestBody?: string;
  responseBody?: string;
}

export interface UserFlowStep {
  step: number;
  title: string;
  description: string;
  screen: string;
  actions: string[];
}

export interface CodeTemplate {
  id: string;
  name: string;
  description: string;
  language: string;
  framework: string;
  files: TemplateFile[];
  dependencies: string[];
  setupInstructions: string[];
}

export interface TemplateFile {
  path: string;
  content: string;
  description: string;
}

export interface AppSubmission {
  appId: string;
  submittedBy: string;
  submissionDate: Date;
  status: 'pending' | 'approved' | 'rejected';
  reviewNotes?: string;
  screenshots: string[];
  demoUrl?: string;
  sourceCode?: string;
}
