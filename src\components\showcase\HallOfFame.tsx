
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Trophy, Star, Download, ExternalLink, Crown } from 'lucide-react';

interface FeaturedApp {
  id: string;
  name: string;
  description: string;
  originalMeme: string;
  creator: string;
  rating: number;
  downloads: number;
  imageUrl: string;
  tags: string[];
  featured: boolean;
}

const HallOfFame = () => {
  const featuredApps: FeaturedApp[] = [
    {
      id: '1',
      name: 'Bug Squasher Pro',
      description: 'Turn debugging frustration into a gamified experience with achievements and leaderboards',
      originalMeme: 'When you fix a bug but create 3 more',
      creator: 'DevMaster2000',
      rating: 4.8,
      downloads: 12500,
      imageUrl: 'https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?w=400',
      tags: ['Productivity', 'Gaming', 'Developer Tools'],
      featured: true
    },
    {
      id: '2',
      name: '<PERSON><PERSON><PERSON>',
      description: 'AI-powered rubber duck debugging companion with voice chat and code analysis',
      originalMeme: 'Me explaining my code to rubber duck',
      creator: '<PERSON><PERSON>hisper<PERSON>',
      rating: 4.7,
      downloads: 8900,
      imageUrl: 'https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=400',
      tags: ['AI', 'Debugging', 'Chat'],
      featured: false
    },
    {
      id: '3',
      name: 'CSS Centering Wizard',
      description: 'Never struggle with centering elements again - visual CSS generator for perfect layouts',
      originalMeme: 'CSS centering div in 2024',
      creator: 'LayoutLegend',
      rating: 4.9,
      downloads: 15600,
      imageUrl: 'https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400',
      tags: ['CSS', 'Tools', 'Design'],
      featured: true
    }
  ];

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star 
        key={i} 
        className={`h-4 w-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-600'}`} 
      />
    ));
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-4xl font-bold text-white mb-4 flex items-center justify-center gap-2">
          <Trophy className="h-8 w-8 text-yellow-400" />
          Hall of Fame
        </h2>
        <p className="text-slate-400 text-lg">
          Legendary meme-to-app transformations that made it big
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {featuredApps.map((app) => (
          <Card 
            key={app.id} 
            className={`bg-black/30 backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:shadow-lg ${
              app.featured 
                ? 'border-yellow-500/50 hover:border-yellow-400/70 hover:shadow-yellow-500/20' 
                : 'border-purple-800/30 hover:border-purple-600/50 hover:shadow-purple-500/20'
            }`}
          >
            {app.featured && (
              <div className="absolute -top-2 -right-2 z-10">
                <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                  <Crown className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              </div>
            )}
            
            <div className="relative">
              <img 
                src={app.imageUrl} 
                alt={app.name}
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <div className="absolute top-2 left-2">
                <Badge className="bg-black/70 text-white">
                  #{app.id}
                </Badge>
              </div>
            </div>
            
            <CardHeader className="pb-2">
              <CardTitle className="text-white text-xl flex items-center justify-between">
                {app.name}
                <div className="flex items-center gap-1">
                  {getRatingStars(app.rating)}
                  <span className="text-sm text-slate-400 ml-1">({app.rating})</span>
                </div>
              </CardTitle>
              <CardDescription className="text-slate-300">
                {app.description}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm text-purple-300">
                  <strong>Original Meme:</strong> "{app.originalMeme}"
                </p>
                <p className="text-sm text-slate-400">
                  <strong>Creator:</strong> {app.creator}
                </p>
                <p className="text-sm text-green-400">
                  <strong>Downloads:</strong> {app.downloads.toLocaleString()}
                </p>
              </div>
              
              <div className="flex flex-wrap gap-1">
                {app.tags.map((tag, index) => (
                  <Badge 
                    key={index} 
                    className="text-xs bg-purple-600/20 text-purple-300 border-purple-500/30"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
              
              <div className="flex gap-2">
                <Button 
                  className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
                  size="sm"
                >
                  <Download className="h-4 w-4 mr-1" />
                  Get App
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  className="border-purple-500/50 text-purple-300 hover:bg-purple-900/30"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center py-8">
        <Card className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 border-purple-500/30 backdrop-blur-sm max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="text-white text-2xl">
              🎉 Submit Your Success Story
            </CardTitle>
            <CardDescription className="text-slate-300">
              Created an amazing app from a meme? Share it with the community and get featured!
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white">
              <Trophy className="h-4 w-4 mr-2" />
              Submit for Hall of Fame
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default HallOfFame;
