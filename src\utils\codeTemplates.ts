import { GeneratedApp, CodeTemplate } from '@/types/app';

/**
 * Utility for generating code templates based on app concepts
 */

export class CodeTemplateGenerator {
  /**
   * Generate a complete code template for an app
   */
  static generateTemplate(app: GeneratedApp): CodeTemplate {
    const framework = this.selectFramework(app.techStack);
    const language = this.selectLanguage(app.techStack);
    
    return {
      id: `template_${app.id}`,
      name: `${app.name} Starter Template`,
      description: `Complete ${framework} starter template for ${app.name} - ${app.description}`,
      language,
      framework,
      files: this.generateFiles(app, framework, language),
      dependencies: this.generateDependencies(app.techStack, framework),
      setupInstructions: this.generateSetupInstructions(app, framework)
    };
  }

  /**
   * Select the primary framework based on tech stack
   */
  private static selectFramework(techStack: string[]): string {
    const frameworkPriority = ['Next.js', 'React', 'Vue.js', 'Angular', 'Svelte'];
    
    for (const framework of frameworkPriority) {
      if (techStack.includes(framework)) {
        return framework;
      }
    }
    
    // Default to React if no specific framework found
    return 'React';
  }

  /**
   * Select the primary language based on tech stack
   */
  private static selectLanguage(techStack: string[]): string {
    if (techStack.includes('TypeScript')) return 'TypeScript';
    if (techStack.includes('JavaScript')) return 'JavaScript';
    return 'TypeScript'; // Default to TypeScript
  }

  /**
   * Generate all necessary files for the template
   */
  private static generateFiles(app: GeneratedApp, framework: string, language: string): any[] {
    const files: any[] = [];
    
    // Package.json
    files.push(this.generatePackageJson(app, framework));
    
    // Main app file
    files.push(this.generateMainAppFile(app, framework, language));
    
    // Component files
    files.push(...this.generateComponentFiles(app, framework, language));
    
    // Configuration files
    files.push(...this.generateConfigFiles(app, framework, language));
    
    // Documentation
    files.push(this.generateReadme(app, framework));
    
    // Environment file
    files.push(this.generateEnvFile(app));
    
    return files;
  }

  /**
   * Generate package.json file
   */
  private static generatePackageJson(app: GeneratedApp, framework: string): any {
    const basePackage = {
      name: app.name.toLowerCase().replace(/\s+/g, '-'),
      version: '1.0.0',
      description: app.description,
      main: framework === 'Next.js' ? 'next.config.js' : 'src/index.tsx',
      scripts: this.getScripts(framework),
      keywords: app.tags,
      author: app.creator,
      license: 'MIT'
    };

    return {
      path: 'package.json',
      content: JSON.stringify(basePackage, null, 2),
      description: 'Project configuration and dependencies'
    };
  }

  /**
   * Get npm scripts based on framework
   */
  private static getScripts(framework: string): Record<string, string> {
    const baseScripts = {
      'React': {
        'dev': 'vite dev',
        'build': 'vite build',
        'preview': 'vite preview',
        'lint': 'eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0',
        'type-check': 'tsc --noEmit'
      },
      'Next.js': {
        'dev': 'next dev',
        'build': 'next build',
        'start': 'next start',
        'lint': 'next lint',
        'type-check': 'tsc --noEmit'
      },
      'Vue.js': {
        'dev': 'vite dev',
        'build': 'vite build',
        'preview': 'vite preview',
        'lint': 'eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore'
      }
    };

    return baseScripts[framework as keyof typeof baseScripts] || baseScripts['React'];
  }

  /**
   * Generate main application file
   */
  private static generateMainAppFile(app: GeneratedApp, framework: string, language: string): any {
    const ext = language === 'TypeScript' ? 'tsx' : 'jsx';
    
    let content = '';
    
    if (framework === 'React' || framework === 'Next.js') {
      content = this.generateReactApp(app, framework === 'Next.js');
    } else if (framework === 'Vue.js') {
      content = this.generateVueApp(app);
    }

    return {
      path: framework === 'Next.js' ? `pages/_app.${ext}` : `src/App.${ext}`,
      content,
      description: 'Main application component'
    };
  }

  /**
   * Generate React/Next.js app content
   */
  private static generateReactApp(app: GeneratedApp, isNextJs: boolean): string {
    const imports = isNextJs 
      ? `import type { AppProps } from 'next/app'
import Head from 'next/head'
import '../styles/globals.css'`
      : `import React from 'react'
import './App.css'`;

    const componentName = isNextJs ? 'MyApp' : 'App';
    const props = isNextJs ? '{ Component, pageProps }: AppProps' : '';

    return `${imports}

/**
 * ${app.name} - ${app.description}
 * Generated from meme: "${app.originalMemeTitle}"
 */

function ${componentName}(${props}) {
  return (
    ${isNextJs ? `<>
      <Head>
        <title>${app.name}</title>
        <meta name="description" content="${app.description}" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <Component {...pageProps} />
    </>` : `<div className="App">
      <header className="App-header">
        <h1>${app.name}</h1>
        <p>${app.description}</p>
        <div className="features">
          <h2>Key Features:</h2>
          <ul>
            ${app.features.slice(0, 5).map(feature => `<li>${feature.name}</li>`).join('\n            ')}
          </ul>
        </div>
      </header>
    </div>`}
  )
}

export default ${componentName}`;
  }

  /**
   * Generate Vue.js app content
   */
  private static generateVueApp(app: GeneratedApp): string {
    return `<template>
  <div id="app">
    <header class="app-header">
      <h1>${app.name}</h1>
      <p>${app.description}</p>
      <div class="features">
        <h2>Key Features:</h2>
        <ul>
          ${app.features.slice(0, 5).map(feature => `<li>${feature.name}</li>`).join('\n          ')}
        </ul>
      </div>
    </header>
  </div>
</template>

<script setup lang="ts">
// ${app.name} - ${app.description}
// Generated from meme: "${app.originalMemeTitle}"

import { ref, onMounted } from 'vue'

const appName = ref('${app.name}')

onMounted(() => {
  console.log('${app.name} initialized!')
})
</script>

<style scoped>
.app-header {
  text-align: center;
  padding: 2rem;
}

.features {
  margin-top: 2rem;
}

.features ul {
  list-style-type: none;
  padding: 0;
}

.features li {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: #f0f0f0;
  border-radius: 4px;
}
</style>`;
  }

  /**
   * Generate component files
   */
  private static generateComponentFiles(app: GeneratedApp, framework: string, language: string): any[] {
    const ext = language === 'TypeScript' ? 'tsx' : 'jsx';
    const files: any[] = [];

    // Generate a component for each main feature
    app.features.slice(0, 3).forEach((feature, index) => {
      const componentName = feature.name.replace(/\s+/g, '');
      const fileName = `src/components/${componentName}.${ext}`;
      
      files.push({
        path: fileName,
        content: this.generateFeatureComponent(feature, componentName, framework),
        description: `Component for ${feature.name} feature`
      });
    });

    return files;
  }

  /**
   * Generate a component for a specific feature
   */
  private static generateFeatureComponent(feature: any, componentName: string, framework: string): string {
    if (framework === 'Vue.js') {
      return `<template>
  <div class="${componentName.toLowerCase()}">
    <h2>${feature.name}</h2>
    <p>${feature.description}</p>
    <!-- TODO: Implement ${feature.name} functionality -->
  </div>
</template>

<script setup lang="ts">
// ${feature.name} Component
// Priority: ${feature.priority}
// Estimated hours: ${feature.estimatedHours}
</script>

<style scoped>
.${componentName.toLowerCase()} {
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 1rem 0;
}
</style>`;
    }

    // React/Next.js component
    return `import React from 'react'

interface ${componentName}Props {
  // Add props here
}

/**
 * ${feature.name} Component
 * Priority: ${feature.priority}
 * Estimated hours: ${feature.estimatedHours}
 */
const ${componentName}: React.FC<${componentName}Props> = () => {
  return (
    <div className="${componentName.toLowerCase()}">
      <h2>${feature.name}</h2>
      <p>${feature.description}</p>
      {/* TODO: Implement ${feature.name} functionality */}
    </div>
  )
}

export default ${componentName}`;
  }

  /**
   * Generate configuration files
   */
  private static generateConfigFiles(app: GeneratedApp, framework: string, language: string): any[] {
    const files: any[] = [];

    // TypeScript config
    if (language === 'TypeScript') {
      files.push({
        path: 'tsconfig.json',
        content: this.generateTsConfig(framework),
        description: 'TypeScript configuration'
      });
    }

    // Vite config (for React)
    if (framework === 'React') {
      files.push({
        path: 'vite.config.ts',
        content: this.generateViteConfig(),
        description: 'Vite build configuration'
      });
    }

    // Next.js config
    if (framework === 'Next.js') {
      files.push({
        path: 'next.config.js',
        content: this.generateNextConfig(),
        description: 'Next.js configuration'
      });
    }

    // ESLint config
    files.push({
      path: '.eslintrc.json',
      content: this.generateEslintConfig(framework),
      description: 'ESLint configuration'
    });

    return files;
  }

  /**
   * Generate TypeScript configuration
   */
  private static generateTsConfig(framework: string): string {
    const config = {
      compilerOptions: {
        target: 'ES2020',
        useDefineForClassFields: true,
        lib: ['ES2020', 'DOM', 'DOM.Iterable'],
        module: 'ESNext',
        skipLibCheck: true,
        moduleResolution: 'bundler',
        allowImportingTsExtensions: true,
        resolveJsonModule: true,
        isolatedModules: true,
        noEmit: true,
        jsx: 'react-jsx',
        strict: true,
        noUnusedLocals: true,
        noUnusedParameters: true,
        noFallthroughCasesInSwitch: true,
        baseUrl: '.',
        paths: {
          '@/*': ['./src/*']
        }
      },
      include: ['src'],
      references: [{ path: './tsconfig.node.json' }]
    };

    return JSON.stringify(config, null, 2);
  }

  /**
   * Generate other config files
   */
  private static generateViteConfig(): string {
    return `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
})`;
  }

  private static generateNextConfig(): string {
    return `/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
}

module.exports = nextConfig`;
  }

  private static generateEslintConfig(framework: string): string {
    const config = {
      extends: [
        'eslint:recommended',
        '@typescript-eslint/recommended',
        framework === 'React' ? 'plugin:react-hooks/recommended' : null,
        framework === 'Next.js' ? 'next/core-web-vitals' : null,
        framework === 'Vue.js' ? 'plugin:vue/vue3-essential' : null
      ].filter(Boolean),
      ignorePatterns: ['dist', '.eslintrc.cjs'],
      parser: '@typescript-eslint/parser',
      plugins: ['@typescript-eslint'],
      rules: {
        '@typescript-eslint/no-unused-vars': 'warn',
        '@typescript-eslint/no-explicit-any': 'warn'
      }
    };

    return JSON.stringify(config, null, 2);
  }

  /**
   * Generate README file
   */
  private static generateReadme(app: GeneratedApp, framework: string): any {
    const content = `# ${app.name}

${app.description}

*Generated from the viral meme: "${app.originalMemeTitle}"*

## Features

${app.features.map(feature => `- **${feature.name}**: ${feature.description}`).join('\n')}

## Tech Stack

${app.techStack.map(tech => `- ${tech}`).join('\n')}

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository
\`\`\`bash
git clone <repository-url>
cd ${app.name.toLowerCase().replace(/\s+/g, '-')}
\`\`\`

2. Install dependencies
\`\`\`bash
npm install
\`\`\`

3. Set up environment variables
\`\`\`bash
cp .env.example .env.local
\`\`\`

4. Start the development server
\`\`\`bash
npm run dev
\`\`\`

## Development

### Available Scripts

- \`npm run dev\` - Start development server
- \`npm run build\` - Build for production
- \`npm run lint\` - Run ESLint
- \`npm run type-check\` - Run TypeScript type checking

### Project Structure

\`\`\`
src/
├── components/     # Reusable components
├── pages/         # Application pages
├── styles/        # CSS and styling
├── utils/         # Utility functions
└── types/         # TypeScript type definitions
\`\`\`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Acknowledgments

- Inspired by the viral meme: "${app.originalMemeTitle}"
- Built with ${framework}
- Generated by MemeForge AI
`;

    return {
      path: 'README.md',
      content,
      description: 'Project documentation and setup instructions'
    };
  }

  /**
   * Generate environment file
   */
  private static generateEnvFile(app: GeneratedApp): any {
    const content = `# ${app.name} Environment Variables

# Application
NEXT_PUBLIC_APP_NAME="${app.name}"
NEXT_PUBLIC_APP_DESCRIPTION="${app.description}"

# API Configuration
# API_BASE_URL=http://localhost:3000/api
# API_KEY=your_api_key_here

# Database (if applicable)
# DATABASE_URL=your_database_url_here

# Authentication (if applicable)
# NEXTAUTH_SECRET=your_nextauth_secret
# NEXTAUTH_URL=http://localhost:3000

# Third-party Services
# Add your service API keys here

# Development
NODE_ENV=development
`;

    return {
      path: '.env.example',
      content,
      description: 'Environment variables template'
    };
  }

  /**
   * Generate dependencies based on tech stack
   */
  private static generateDependencies(techStack: string[], framework: string): string[] {
    const baseDependencies = ['react@^18.0.0', 'react-dom@^18.0.0'];
    const devDependencies = ['@types/react@^18.0.0', '@types/react-dom@^18.0.0', 'typescript@^5.0.0'];

    // Framework-specific dependencies
    if (framework === 'Next.js') {
      baseDependencies.push('next@^14.0.0');
    } else if (framework === 'React') {
      baseDependencies.push('@vitejs/plugin-react@^4.0.0', 'vite@^5.0.0');
    } else if (framework === 'Vue.js') {
      return ['vue@^3.3.0', '@vitejs/plugin-vue@^4.0.0', 'vite@^5.0.0'];
    }

    // Tech stack specific dependencies
    if (techStack.includes('Tailwind CSS')) {
      baseDependencies.push('tailwindcss@^3.3.0', 'autoprefixer@^10.4.0', 'postcss@^8.4.0');
    }

    if (techStack.includes('Prisma')) {
      baseDependencies.push('prisma@^5.0.0', '@prisma/client@^5.0.0');
    }

    if (techStack.includes('NextAuth.js')) {
      baseDependencies.push('next-auth@^4.0.0');
    }

    return [...baseDependencies, ...devDependencies];
  }

  /**
   * Generate setup instructions
   */
  private static generateSetupInstructions(app: GeneratedApp, framework: string): string[] {
    const instructions = [
      'Clone or download the generated code template',
      'Navigate to the project directory',
      'Install dependencies with `npm install`',
      'Copy `.env.example` to `.env.local` and configure environment variables'
    ];

    if (framework === 'Next.js') {
      instructions.push('Run `npm run dev` to start the Next.js development server');
      instructions.push('Open http://localhost:3000 in your browser');
    } else if (framework === 'React') {
      instructions.push('Run `npm run dev` to start the Vite development server');
      instructions.push('Open http://localhost:5173 in your browser');
    } else if (framework === 'Vue.js') {
      instructions.push('Run `npm run dev` to start the Vue development server');
      instructions.push('Open http://localhost:5173 in your browser');
    }

    instructions.push('Start building your app by implementing the generated components');
    instructions.push('Refer to the README.md for detailed documentation');

    return instructions;
  }
}

export default CodeTemplateGenerator;
