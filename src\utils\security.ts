/**
 * Security utilities for input validation, sanitization, and protection
 */

/**
 * Input validation utilities
 */
export class InputValidator {
  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  /**
   * Validate username format
   */
  static isValidUsername(username: string): boolean {
    // 3-30 characters, alphanumeric and underscores only
    const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
    return usernameRegex.test(username);
  }

  /**
   * Validate URL format
   */
  static isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  }

  /**
   * Validate if URL is from allowed domains
   */
  static isAllowedDomain(url: string, allowedDomains: string[]): boolean {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      return allowedDomains.some(domain => {
        const normalizedDomain = domain.toLowerCase();
        return hostname === normalizedDomain || hostname.endsWith(`.${normalizedDomain}`);
      });
    } catch {
      return false;
    }
  }

  /**
   * Validate Reddit URL format
   */
  static isValidRedditUrl(url: string): boolean {
    const redditDomains = ['reddit.com', 'www.reddit.com', 'old.reddit.com', 'i.redd.it'];
    return this.isValidUrl(url) && this.isAllowedDomain(url, redditDomains);
  }

  /**
   * Validate image URL format
   */
  static isValidImageUrl(url: string): boolean {
    if (!this.isValidUrl(url)) return false;
    
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg)$/i;
    const imageDomains = ['imgur.com', 'i.imgur.com', 'i.redd.it', 'preview.redd.it'];
    
    return imageExtensions.test(url) || this.isAllowedDomain(url, imageDomains);
  }

  /**
   * Validate text length
   */
  static isValidLength(text: string, minLength: number = 0, maxLength: number = 1000): boolean {
    return text.length >= minLength && text.length <= maxLength;
  }

  /**
   * Check for potentially malicious content
   */
  static containsMaliciousContent(text: string): boolean {
    const maliciousPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^>]*>/gi,
      /<object\b[^>]*>/gi,
      /<embed\b[^>]*>/gi,
      /data:text\/html/gi
    ];

    return maliciousPatterns.some(pattern => pattern.test(text));
  }
}

/**
 * Content sanitization utilities
 */
export class ContentSanitizer {
  /**
   * Sanitize HTML content
   */
  static sanitizeHtml(html: string): string {
    // Remove script tags and event handlers
    let sanitized = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+\s*=\s*"[^"]*"/gi, '')
      .replace(/on\w+\s*=\s*'[^']*'/gi, '')
      .replace(/javascript:/gi, '');

    // Remove potentially dangerous tags
    const dangerousTags = ['iframe', 'object', 'embed', 'form', 'input', 'button'];
    dangerousTags.forEach(tag => {
      const regex = new RegExp(`<${tag}\\b[^>]*>.*?<\\/${tag}>`, 'gi');
      sanitized = sanitized.replace(regex, '');
    });

    return sanitized;
  }

  /**
   * Sanitize text for display
   */
  static sanitizeText(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Sanitize URL
   */
  static sanitizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      
      // Only allow http and https protocols
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return '';
      }

      return urlObj.toString();
    } catch {
      return '';
    }
  }

  /**
   * Remove excessive whitespace and normalize text
   */
  static normalizeText(text: string): string {
    return text
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n'); // Normalize line breaks
  }

  /**
   * Filter profanity and inappropriate content
   */
  static filterProfanity(text: string): string {
    // Basic profanity filter - in production, use a more comprehensive solution
    const profanityWords = [
      // Add common profanity words here
      'spam', 'scam', 'phishing'
    ];

    let filtered = text;
    profanityWords.forEach(word => {
      const regex = new RegExp(word, 'gi');
      filtered = filtered.replace(regex, '*'.repeat(word.length));
    });

    return filtered;
  }
}

/**
 * CSRF protection utilities
 */
export class CSRFProtection {
  private static tokens = new Map<string, { token: string; expires: number }>();

  /**
   * Generate CSRF token
   */
  static generateToken(sessionId: string): string {
    const token = this.generateRandomString(32);
    const expires = Date.now() + (60 * 60 * 1000); // 1 hour

    this.tokens.set(sessionId, { token, expires });
    return token;
  }

  /**
   * Validate CSRF token
   */
  static validateToken(sessionId: string, token: string): boolean {
    const stored = this.tokens.get(sessionId);
    
    if (!stored) return false;
    if (stored.expires < Date.now()) {
      this.tokens.delete(sessionId);
      return false;
    }

    return stored.token === token;
  }

  /**
   * Clean up expired tokens
   */
  static cleanup(): void {
    const now = Date.now();
    for (const [sessionId, data] of this.tokens.entries()) {
      if (data.expires < now) {
        this.tokens.delete(sessionId);
      }
    }
  }

  private static generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

/**
 * Request validation utilities
 */
export class RequestValidator {
  /**
   * Validate request origin
   */
  static isValidOrigin(origin: string, allowedOrigins: string[]): boolean {
    return allowedOrigins.includes(origin);
  }

  /**
   * Validate request size
   */
  static isValidSize(contentLength: number, maxSize: number = 1024 * 1024): boolean { // 1MB default
    return contentLength <= maxSize;
  }

  /**
   * Validate content type
   */
  static isValidContentType(contentType: string, allowedTypes: string[]): boolean {
    return allowedTypes.some(type => contentType.includes(type));
  }

  /**
   * Check for suspicious request patterns
   */
  static isSuspiciousRequest(request: {
    userAgent?: string;
    referer?: string;
    ip?: string;
  }): boolean {
    // Check for bot-like user agents
    const suspiciousUserAgents = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i
    ];

    if (request.userAgent) {
      const isSuspiciousUA = suspiciousUserAgents.some(pattern => 
        pattern.test(request.userAgent!)
      );
      if (isSuspiciousUA) return true;
    }

    // Check for suspicious referers
    if (request.referer) {
      const suspiciousDomains = ['malicious.com', 'spam.com'];
      const isSuspiciousReferer = suspiciousDomains.some(domain => 
        request.referer!.includes(domain)
      );
      if (isSuspiciousReferer) return true;
    }

    return false;
  }
}

/**
 * Security headers utilities
 */
export class SecurityHeaders {
  /**
   * Get recommended security headers
   */
  static getSecurityHeaders(): Record<string, string> {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Content-Security-Policy': this.getCSPHeader(),
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
    };
  }

  /**
   * Generate Content Security Policy header
   */
  private static getCSPHeader(): string {
    const directives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Note: unsafe-* should be avoided in production
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self' https://www.reddit.com https://api.reddit.com",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ];

    return directives.join('; ');
  }
}

/**
 * Error handling utilities
 */
export class SecurityErrorHandler {
  /**
   * Sanitize error messages for client response
   */
  static sanitizeError(error: Error, isDevelopment: boolean = false): string {
    if (isDevelopment) {
      return error.message;
    }

    // In production, return generic messages to avoid information disclosure
    const genericMessages: Record<string, string> = {
      'ValidationError': 'Invalid input provided',
      'RateLimitError': 'Too many requests. Please try again later.',
      'AuthenticationError': 'Authentication required',
      'AuthorizationError': 'Access denied',
      'NotFoundError': 'Resource not found'
    };

    return genericMessages[error.constructor.name] || 'An error occurred';
  }

  /**
   * Log security events
   */
  static logSecurityEvent(event: {
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    ip?: string;
    userId?: string;
    userAgent?: string;
    timestamp?: Date;
  }): void {
    const logEntry = {
      ...event,
      timestamp: event.timestamp || new Date(),
      id: this.generateEventId()
    };

    // In production, send to security monitoring service
    console.warn('Security Event:', logEntry);
  }

  private static generateEventId(): string {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Main security utility class
 */
export class Security {
  /**
   * Validate and sanitize user input
   */
  static validateInput(input: {
    email?: string;
    username?: string;
    text?: string;
    url?: string;
  }): { isValid: boolean; errors: string[]; sanitized: any } {
    const errors: string[] = [];
    const sanitized: any = {};

    if (input.email !== undefined) {
      if (!InputValidator.isValidEmail(input.email)) {
        errors.push('Invalid email format');
      } else {
        sanitized.email = input.email.toLowerCase().trim();
      }
    }

    if (input.username !== undefined) {
      if (!InputValidator.isValidUsername(input.username)) {
        errors.push('Username must be 3-30 characters, alphanumeric and underscores only');
      } else {
        sanitized.username = input.username.trim();
      }
    }

    if (input.text !== undefined) {
      if (InputValidator.containsMaliciousContent(input.text)) {
        errors.push('Text contains potentially malicious content');
      } else {
        sanitized.text = ContentSanitizer.normalizeText(
          ContentSanitizer.filterProfanity(input.text)
        );
      }
    }

    if (input.url !== undefined) {
      if (!InputValidator.isValidUrl(input.url)) {
        errors.push('Invalid URL format');
      } else {
        sanitized.url = ContentSanitizer.sanitizeUrl(input.url);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitized
    };
  }

  /**
   * Check if request is secure
   */
  static isSecureRequest(request: {
    origin?: string;
    userAgent?: string;
    contentType?: string;
    contentLength?: number;
    ip?: string;
  }): boolean {
    // Check for suspicious patterns
    if (RequestValidator.isSuspiciousRequest(request)) {
      return false;
    }

    // Validate content size
    if (request.contentLength && !RequestValidator.isValidSize(request.contentLength)) {
      return false;
    }

    return true;
  }
}

// Periodic cleanup of CSRF tokens
setInterval(() => {
  CSRFProtection.cleanup();
}, 60 * 60 * 1000); // Every hour

export default Security;
