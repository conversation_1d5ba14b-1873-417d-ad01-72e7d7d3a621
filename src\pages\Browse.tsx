import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search, 
  Filter, 
  TrendingUp, 
  Clock, 
  Star,
  Grid3X3,
  List,
  RefreshCw
} from 'lucide-react';
import { useRedditAPI } from '@/hooks/useRedditAPI';
import { SUBREDDIT_CONFIGS } from '@/types/meme';
import TrendingMemes from '@/components/memes/TrendingMemes';
import MemeGrid from '@/components/memes/MemeGrid';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Browse = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubreddit, setSelectedSubreddit] = useState('all');
  const [sortBy, setSortBy] = useState<'hot' | 'new' | 'top'>('hot');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState('trending');

  const { 
    memes, 
    isLoading, 
    error, 
    refetch,
    getTrendingMemes,
    getHighPotentialMemes,
    searchMemes 
  } = useRedditAPI(selectedSubreddit);

  const handleSearch = () => {
    if (searchQuery.trim()) {
      searchMemes.mutate(searchQuery);
    }
  };

  const handleRefresh = () => {
    refetch();
  };

  const filteredMemes = () => {
    switch (activeTab) {
      case 'trending':
        return getTrendingMemes();
      case 'high-potential':
        return getHighPotentialMemes();
      case 'search':
        return searchMemes.data || [];
      default:
        return memes;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-purple-400 via-pink-400 to-purple-600 bg-clip-text text-transparent">
            Browse Memes
          </h1>
          <p className="text-xl text-slate-300 mb-6 max-w-2xl mx-auto">
            Discover trending memes with high app generation potential from across Reddit
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="bg-black/30 border-purple-800/30 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-end">
              {/* Search */}
              <div className="flex-1">
                <label className="text-sm font-medium text-slate-300 mb-2 block">
                  Search Memes
                </label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Search for memes, keywords, or themes..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400"
                  />
                  <Button 
                    onClick={handleSearch}
                    disabled={searchMemes.isPending}
                    className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Subreddit Filter */}
              <div className="w-full lg:w-48">
                <label className="text-sm font-medium text-slate-300 mb-2 block">
                  Subreddit
                </label>
                <Select value={selectedSubreddit} onValueChange={setSelectedSubreddit}>
                  <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-600">
                    <SelectItem value="all">All Subreddits</SelectItem>
                    {SUBREDDIT_CONFIGS.map((config) => (
                      <SelectItem key={config.name} value={config.name}>
                        r/{config.displayName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Sort Filter */}
              <div className="w-full lg:w-32">
                <label className="text-sm font-medium text-slate-300 mb-2 block">
                  Sort By
                </label>
                <Select value={sortBy} onValueChange={(value: 'hot' | 'new' | 'top') => setSortBy(value)}>
                  <SelectTrigger className="bg-slate-800/50 border-slate-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-600">
                    <SelectItem value="hot">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-3 w-3" />
                        Hot
                      </div>
                    </SelectItem>
                    <SelectItem value="new">
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3" />
                        New
                      </div>
                    </SelectItem>
                    <SelectItem value="top">
                      <div className="flex items-center gap-2">
                        <Star className="h-3 w-3" />
                        Top
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* View Mode Toggle */}
              <div className="flex gap-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="border-slate-600"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="border-slate-600"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              {/* Refresh Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
                className="border-slate-600 text-slate-300"
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-black/30 border border-slate-700 mb-8">
            <TabsTrigger value="trending" className="data-[state=active]:bg-purple-600/30">
              <TrendingUp className="h-4 w-4 mr-2" />
              Trending
            </TabsTrigger>
            <TabsTrigger value="high-potential" className="data-[state=active]:bg-purple-600/30">
              <Star className="h-4 w-4 mr-2" />
              High Potential
            </TabsTrigger>
            <TabsTrigger value="all" className="data-[state=active]:bg-purple-600/30">
              All Memes
            </TabsTrigger>
            <TabsTrigger value="search" className="data-[state=active]:bg-purple-600/30">
              <Search className="h-4 w-4 mr-2" />
              Search Results
            </TabsTrigger>
          </TabsList>

          {/* Loading State */}
          {isLoading && (
            <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-8 text-center">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-400" />
                <p className="text-slate-300">Loading memes...</p>
              </CardContent>
            </Card>
          )}

          {/* Error State */}
          {error && (
            <Card className="bg-red-900/30 border-red-500/50 backdrop-blur-sm">
              <CardContent className="p-8 text-center">
                <p className="text-red-300 mb-4">Failed to load memes: {error.message}</p>
                <Button onClick={handleRefresh} variant="outline" className="border-red-500/50 text-red-300">
                  Try Again
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Trending Tab */}
          <TabsContent value="trending">
            <TrendingMemes />
          </TabsContent>

          {/* High Potential Tab */}
          <TabsContent value="high-potential">
            <div className="space-y-6">
              <Card className="bg-black/30 border-green-500/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Star className="h-6 w-6 text-green-400" />
                    High App Potential Memes
                  </CardTitle>
                  <CardDescription className="text-slate-300">
                    Memes with 80%+ app generation potential based on engagement and themes
                  </CardDescription>
                </CardHeader>
              </Card>
              <MemeGrid memes={getHighPotentialMemes()} viewMode={viewMode} />
            </div>
          </TabsContent>

          {/* All Memes Tab */}
          <TabsContent value="all">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <h2 className="text-2xl font-bold text-white">All Memes</h2>
                  <Badge className="bg-blue-600/20 text-blue-300 border-blue-500/30">
                    {memes.length} found
                  </Badge>
                </div>
                <div className="text-sm text-slate-400">
                  From r/{selectedSubreddit} • Sorted by {sortBy}
                </div>
              </div>
              <MemeGrid memes={memes} viewMode={viewMode} />
            </div>
          </TabsContent>

          {/* Search Results Tab */}
          <TabsContent value="search">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <h2 className="text-2xl font-bold text-white">Search Results</h2>
                  {searchMemes.data && (
                    <Badge className="bg-purple-600/20 text-purple-300 border-purple-500/30">
                      {searchMemes.data.length} results
                    </Badge>
                  )}
                </div>
                {searchQuery && (
                  <div className="text-sm text-slate-400">
                    Results for "{searchQuery}"
                  </div>
                )}
              </div>
              
              {searchMemes.isPending && (
                <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-8 text-center">
                    <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-400" />
                    <p className="text-slate-300">Searching memes...</p>
                  </CardContent>
                </Card>
              )}
              
              {searchMemes.data && searchMemes.data.length > 0 ? (
                <MemeGrid memes={searchMemes.data} viewMode={viewMode} />
              ) : searchMemes.data && searchMemes.data.length === 0 ? (
                <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-8 text-center">
                    <Search className="h-12 w-12 mx-auto mb-4 text-slate-400 opacity-50" />
                    <p className="text-slate-300 text-lg mb-2">No results found</p>
                    <p className="text-slate-400">Try different keywords or browse trending memes instead</p>
                  </CardContent>
                </Card>
              ) : (
                <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
                  <CardContent className="p-8 text-center">
                    <Search className="h-12 w-12 mx-auto mb-4 text-slate-400 opacity-50" />
                    <p className="text-slate-300 text-lg mb-2">Start Searching</p>
                    <p className="text-slate-400">Enter keywords above to find specific memes</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <Footer />
    </div>
  );
};

export default Browse;
