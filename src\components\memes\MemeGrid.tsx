
import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Search, Filter, TrendingUp, Clock, Star, Grid3X3, List } from 'lucide-react';
import { Meme } from '@/types/meme';
import MemeCard from './MemeCard';

interface MemeGridProps {
  memes: Meme[];
  viewMode?: 'grid' | 'list';
  onMemeAnalyze?: (meme: Meme) => void;
  showFilters?: boolean;
}

const MemeGrid = ({
  memes,
  viewMode = 'grid',
  onMemeAnalyze = () => {},
  showFilters = true
}: MemeGridProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('trending');
  const [filterBy, setFilterBy] = useState('all');

  const filteredAndSortedMemes = memes
    .filter(meme => {
      const matchesSearch = meme.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           meme.subreddit.toLowerCase().includes(searchTerm.toLowerCase());
      
      if (filterBy === 'trending') return matchesSearch && meme.trending;
      if (filterBy === 'high-potential') return matchesSearch && meme.appPotential >= 80;
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'upvotes':
          return b.upvotes - a.upvotes;
        case 'potential':
          return b.appPotential - a.appPotential;
        case 'comments':
          return b.comments - a.comments;
        default:
          return b.trending ? 1 : -1;
      }
    });

  return (
    <div className="space-y-6">
      {/* Search and Filter Controls */}
      {showFilters && (
        <div className="flex flex-col md:flex-row gap-4 items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search memes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-black/30 border-purple-800/30 text-white placeholder:text-slate-400"
            />
          </div>

          <div className="flex gap-2">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-40 bg-black/30 border-purple-800/30 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-black border-purple-800/30">
                <SelectItem value="trending">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Trending
                  </div>
                </SelectItem>
                <SelectItem value="upvotes">
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4" />
                    Most Upvoted
                  </div>
                </SelectItem>
                <SelectItem value="potential">App Potential</SelectItem>
                <SelectItem value="comments">Most Discussed</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterBy} onValueChange={setFilterBy}>
              <SelectTrigger className="w-40 bg-black/30 border-purple-800/30 text-white">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-black border-purple-800/30">
                <SelectItem value="all">All Memes</SelectItem>
                <SelectItem value="trending">Trending Only</SelectItem>
                <SelectItem value="high-potential">High Potential</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      {/* Filter Badges */}
      <div className="flex flex-wrap gap-2">
        {searchTerm && (
          <Badge variant="outline" className="border-purple-500/50 text-purple-300">
            Search: "{searchTerm}"
          </Badge>
        )}
        {filterBy !== 'all' && (
          <Badge variant="outline" className="border-purple-500/50 text-purple-300">
            Filter: {filterBy.replace('-', ' ')}
          </Badge>
        )}
        <Badge variant="outline" className="border-slate-500/50 text-slate-300">
          {filteredAndSortedMemes.length} memes found
        </Badge>
      </div>

      {/* Memes Grid */}
      {filteredAndSortedMemes.length > 0 ? (
        viewMode === 'grid' ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredAndSortedMemes.map((meme) => (
              <MemeCard
                key={meme.id}
                meme={meme}
                onAnalyze={onMemeAnalyze}
              />
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredAndSortedMemes.map((meme) => (
              <Card key={meme.id} className="bg-black/30 border-purple-800/30 backdrop-blur-sm">
                <CardContent className="p-4">
                  <div className="flex gap-4">
                    <img
                      src={meme.imageUrl}
                      alt={meme.title}
                      className="w-24 h-24 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h3 className="text-white font-semibold mb-2">{meme.title}</h3>
                      <div className="flex items-center gap-4 text-sm text-slate-400 mb-3">
                        <span>r/{meme.subreddit}</span>
                        <span>{meme.upvotes.toLocaleString()} upvotes</span>
                        <span>{meme.comments} comments</span>
                        <Badge className={`text-xs ${
                          meme.appPotential >= 90 ? 'bg-green-600/20 text-green-300' :
                          meme.appPotential >= 80 ? 'bg-yellow-600/20 text-yellow-300' :
                          'bg-red-600/20 text-red-300'
                        }`}>
                          {meme.appPotential}% potential
                        </Badge>
                      </div>
                      <Button
                        onClick={() => onMemeAnalyze(meme)}
                        size="sm"
                        className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                      >
                        Analyze
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )
      ) : (
        <div className="text-center py-16">
          <div className="text-slate-400 text-lg mb-4">
            No memes found matching your criteria
          </div>
          <Button 
            onClick={() => {
              setSearchTerm('');
              setFilterBy('all');
            }}
            variant="outline"
            className="border-purple-500/50 text-purple-300 hover:bg-purple-900/30"
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
};

export default MemeGrid;
