import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { aiService } from '@/services/aiService';
import { Meme, MemeAnalysis } from '@/types/meme';
import { GeneratedApp, CodeTemplate } from '@/types/app';

export const useMemeAnalysis = () => {
  const queryClient = useQueryClient();
  const [currentAnalysis, setCurrentAnalysis] = useState<MemeAnalysis | null>(null);
  const [currentApp, setCurrentApp] = useState<GeneratedApp | null>(null);

  // Analyze meme mutation
  const analyzeMeme = useMutation({
    mutationFn: (meme: Meme) => aiService.analyzeMeme(meme),
    onSuccess: (analysis) => {
      setCurrentAnalysis(analysis);
      // Cache the analysis
      queryClient.setQueryData(['meme-analysis', analysis.memeId], analysis);
    },
    onError: (error) => {
      console.error('Meme analysis failed:', error);
    },
  });

  // Generate app concept mutation
  const generateAppConcept = useMutation({
    mutationFn: ({ meme, analysis }: { meme: Meme; analysis: MemeAnalysis }) => 
      aiService.generateAppConcept(meme, analysis),
    onSuccess: (app) => {
      setCurrentApp(app);
      // Cache the generated app
      queryClient.setQueryData(['generated-app', app.id], app);
      // Add to user's generated apps list
      queryClient.setQueryData(['user-apps'], (oldApps: GeneratedApp[] = []) => [app, ...oldApps]);
    },
    onError: (error) => {
      console.error('App concept generation failed:', error);
    },
  });

  // Generate code template mutation
  const generateCodeTemplate = useMutation({
    mutationFn: (app: GeneratedApp) => aiService.generateCodeTemplate(app),
    onSuccess: (template) => {
      // Cache the code template
      queryClient.setQueryData(['code-template', template.id], template);
    },
    onError: (error) => {
      console.error('Code template generation failed:', error);
    },
  });

  // Get cached analysis for a meme
  const useAnalysis = (memeId: string) => {
    return useQuery({
      queryKey: ['meme-analysis', memeId],
      queryFn: () => {
        // This would typically fetch from a backend, but for now return cached data
        const cached = queryClient.getQueryData(['meme-analysis', memeId]);
        if (cached) return cached;
        throw new Error('Analysis not found');
      },
      enabled: !!memeId,
      staleTime: 30 * 60 * 1000, // 30 minutes
      retry: false,
    });
  };

  // Get user's generated apps
  const useUserApps = () => {
    return useQuery({
      queryKey: ['user-apps'],
      queryFn: () => {
        // In a real app, this would fetch from backend
        return queryClient.getQueryData(['user-apps']) || [];
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  // Get code template for an app
  const useCodeTemplate = (templateId: string) => {
    return useQuery({
      queryKey: ['code-template', templateId],
      queryFn: () => {
        const cached = queryClient.getQueryData(['code-template', templateId]);
        if (cached) return cached;
        throw new Error('Template not found');
      },
      enabled: !!templateId,
      retry: false,
    });
  };

  // Analyze meme and generate app in one flow
  const analyzeAndGenerate = useMutation({
    mutationFn: async (meme: Meme) => {
      // First analyze the meme
      const analysis = await aiService.analyzeMeme(meme);
      setCurrentAnalysis(analysis);
      
      // Then generate app concept
      const app = await aiService.generateAppConcept(meme, analysis);
      setCurrentApp(app);
      
      return { analysis, app };
    },
    onSuccess: ({ analysis, app }) => {
      // Cache both results
      queryClient.setQueryData(['meme-analysis', analysis.memeId], analysis);
      queryClient.setQueryData(['generated-app', app.id], app);
      queryClient.setQueryData(['user-apps'], (oldApps: GeneratedApp[] = []) => [app, ...oldApps]);
    },
    onError: (error) => {
      console.error('Analysis and generation failed:', error);
    },
  });

  // Helper functions
  const getAnalysisProgress = () => {
    if (analyzeMeme.isPending) return 'analyzing';
    if (generateAppConcept.isPending) return 'generating';
    if (generateCodeTemplate.isPending) return 'creating-template';
    if (analyzeAndGenerate.isPending) return 'processing';
    return 'idle';
  };

  const isProcessing = () => {
    return analyzeMeme.isPending || 
           generateAppConcept.isPending || 
           generateCodeTemplate.isPending ||
           analyzeAndGenerate.isPending;
  };

  const getError = () => {
    return analyzeMeme.error || 
           generateAppConcept.error || 
           generateCodeTemplate.error ||
           analyzeAndGenerate.error;
  };

  const getRateLimitStatus = () => aiService.getRateLimitStatus();

  const clearCache = () => {
    aiService.clearCache();
    queryClient.invalidateQueries({ queryKey: ['meme-analysis'] });
    queryClient.invalidateQueries({ queryKey: ['generated-app'] });
    queryClient.invalidateQueries({ queryKey: ['code-template'] });
  };

  const resetCurrentState = () => {
    setCurrentAnalysis(null);
    setCurrentApp(null);
  };

  // Statistics and insights
  const getAnalysisStats = () => {
    const userApps = queryClient.getQueryData(['user-apps']) as GeneratedApp[] || [];
    
    return {
      totalAppsGenerated: userApps.length,
      averageAppPotential: userApps.length > 0 
        ? Math.round(userApps.reduce((sum, app) => sum + (app.blueprint?.components?.length || 0), 0) / userApps.length)
        : 0,
      mostUsedTechStack: getMostUsedTechStack(userApps),
      successfulApps: userApps.filter(app => app.status === 'launched').length,
    };
  };

  const getMostUsedTechStack = (apps: GeneratedApp[]) => {
    const techCount: Record<string, number> = {};
    
    apps.forEach(app => {
      app.techStack.forEach(tech => {
        techCount[tech] = (techCount[tech] || 0) + 1;
      });
    });

    return Object.entries(techCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([tech, count]) => ({ tech, count }));
  };

  const getRecommendations = (meme: Meme) => {
    const recommendations: string[] = [];
    
    // Based on subreddit
    if (meme.subreddit.toLowerCase().includes('programmer')) {
      recommendations.push('Consider developer tools or productivity apps');
    }
    
    if (meme.subreddit.toLowerCase().includes('webdev')) {
      recommendations.push('Web-based solutions would be ideal');
    }

    // Based on engagement
    if (meme.upvotes > 10000) {
      recommendations.push('High engagement suggests broad appeal - consider B2C approach');
    }

    if (meme.comments > 200) {
      recommendations.push('High comment count indicates discussion potential - consider community features');
    }

    // Based on app potential
    if (meme.appPotential > 90) {
      recommendations.push('Excellent app potential - consider full-featured application');
    } else if (meme.appPotential > 70) {
      recommendations.push('Good app potential - start with MVP and iterate');
    } else {
      recommendations.push('Moderate potential - focus on specific niche or unique angle');
    }

    return recommendations;
  };

  return {
    // Mutations
    analyzeMeme,
    generateAppConcept,
    generateCodeTemplate,
    analyzeAndGenerate,

    // Queries
    useAnalysis,
    useUserApps,
    useCodeTemplate,

    // State
    currentAnalysis,
    currentApp,

    // Status
    getAnalysisProgress,
    isProcessing,
    getError,
    getRateLimitStatus,

    // Actions
    clearCache,
    resetCurrentState,

    // Insights
    getAnalysisStats,
    getRecommendations,
  };
};

export default useMemeAnalysis;
