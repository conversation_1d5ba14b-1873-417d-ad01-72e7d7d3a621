import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from '@/contexts/AuthContext';
import AuthModal from '@/components/auth/AuthModal';
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Zap, 
  User, 
  Settings, 
  LogOut, 
  Bell, 
  Search,
  Menu,
  X,
  Crown,
  TrendingUp
} from 'lucide-react';
import { useCommunityData } from '@/hooks/useCommunityData';

interface HeaderProps {
  className?: string;
}

const Header = ({ className }: HeaderProps) => {
  const navigate = useNavigate();
  const { state: authState, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [notifications] = useState(3); // Mock notification count
  
  const { useLeaderboard } = useCommunityData();
  const { data: leaderboard } = useLeaderboard('all-time', 'reputation');

  const userRank = authState.user && leaderboard
    ? leaderboard.entries.findIndex(entry => entry.userId === authState.user!.id) + 1
    : null;

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className={`border-b border-purple-800/30 bg-black/20 backdrop-blur-lg sticky top-0 z-50 ${className}`}>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div
            className="flex items-center space-x-2 cursor-pointer"
            onClick={() => navigate('/')}
          >
            <Zap className="h-8 w-8 text-purple-400" />
            <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              MemeForge
            </h1>
            <Badge className="bg-purple-600/20 text-purple-300 border-purple-500/30 text-xs">
              Beta
            </Badge>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Button
              variant="ghost"
              onClick={() => navigate('/browse')}
              className="text-white hover:text-purple-300"
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Discover
            </Button>
            <Button
              variant="ghost"
              onClick={() => navigate('/generate')}
              className="text-white hover:text-purple-300"
            >
              Generate
            </Button>
            <Button
              variant="ghost"
              onClick={() => navigate('/community')}
              className="text-white hover:text-purple-300"
            >
              Community
            </Button>
            <Button
              variant="ghost"
              onClick={() => navigate('/hall-of-fame')}
              className="text-white hover:text-purple-300"
            >
              <Crown className="h-4 w-4 mr-2" />
              Hall of Fame
            </Button>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <Button 
              variant="ghost" 
              size="sm"
              className="hidden sm:flex text-slate-400 hover:text-white"
            >
              <Search className="h-4 w-4" />
            </Button>

            {authState.isAuthenticated && authState.user ? (
              <>
                {/* Notifications */}
                <div className="relative">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    className="text-slate-400 hover:text-white"
                  >
                    <Bell className="h-4 w-4" />
                  </Button>
                  {notifications > 0 && (
                    <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-red-600 text-white text-xs">
                      {notifications}
                    </Badge>
                  )}
                </div>

                {/* User Menu */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                      <Avatar className="h-10 w-10 border-2 border-purple-400/50">
                        <AvatarImage src={authState.user.avatar} alt={authState.user.username} />
                        <AvatarFallback className="bg-purple-600 text-white">
                          {authState.user.username?.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      {userRank && userRank <= 10 && (
                        <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-yellow-600 text-white text-xs">
                          {userRank}
                        </Badge>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56 bg-slate-900 border-slate-700" align="end">
                    <DropdownMenuLabel className="text-white">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium">{authState.user.username}</p>
                        <div className="flex items-center gap-2">
                          <Badge
                            className="text-xs"
                            style={{
                              backgroundColor: `${authState.user.badge?.color}20`,
                              color: authState.user.badge?.color,
                              borderColor: `${authState.user.badge?.color}50`
                            }}
                          >
                            {authState.user.badge?.icon} {authState.user.badge?.title}
                          </Badge>
                          <span className="text-xs text-slate-400">
                            {authState.user.reputation} rep
                          </span>
                        </div>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-slate-700" />
                    <DropdownMenuItem className="text-slate-300 hover:bg-slate-800 hover:text-white">
                      <User className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-slate-300 hover:bg-slate-800 hover:text-white">
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="bg-slate-700" />
                    <DropdownMenuItem
                      className="text-red-400 hover:bg-red-900/30 hover:text-red-300"
                      onClick={logout}
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <Button 
                onClick={onLogin}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white"
              >
                Sign In
              </Button>
            )}

            {/* Mobile Menu Toggle */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden text-white"
              onClick={toggleMobileMenu}
            >
              {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden mt-4 pb-4 border-t border-purple-800/30">
            <nav className="flex flex-col space-y-2 mt-4">
              <Button
                variant="ghost"
                onClick={() => { navigate('/browse'); setIsMobileMenuOpen(false); }}
                className="justify-start text-white hover:text-purple-300"
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Discover
              </Button>
              <Button
                variant="ghost"
                onClick={() => { navigate('/generate'); setIsMobileMenuOpen(false); }}
                className="justify-start text-white hover:text-purple-300"
              >
                Generate
              </Button>
              <Button
                variant="ghost"
                onClick={() => { navigate('/community'); setIsMobileMenuOpen(false); }}
                className="justify-start text-white hover:text-purple-300"
              >
                Community
              </Button>
              <Button
                variant="ghost"
                onClick={() => { navigate('/hall-of-fame'); setIsMobileMenuOpen(false); }}
                className="justify-start text-white hover:text-purple-300"
              >
                <Crown className="h-4 w-4 mr-2" />
                Hall of Fame
              </Button>
              <Button
                variant="ghost"
                onClick={() => { navigate('/browse'); setIsMobileMenuOpen(false); }}
                className="justify-start text-slate-400 hover:text-white"
              >
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
