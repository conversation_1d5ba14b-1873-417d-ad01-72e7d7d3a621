import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  FileText, 
  Database, 
  Globe, 
  Users, 
  Settings, 
  Code, 
  Layers,
  ArrowRight,
  CheckCircle,
  Download,
  Eye,
  Copy
} from 'lucide-react';
import { GeneratedApp, AppBlueprint as AppBlueprintType } from '@/types/app';

interface AppBlueprintProps {
  app: GeneratedApp;
  onDownloadBlueprint?: () => void;
  onViewCode?: () => void;
  className?: string;
}

const AppBlueprint = ({ app, onDownloadBlueprint, onViewCode, className }: AppBlueprintProps) => {
  const [activeTab, setActiveTab] = useState('overview');
  const blueprint = app.blueprint;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const getComponentIcon = (type: string) => {
    switch (type) {
      case 'page': return <FileText className="h-4 w-4" />;
      case 'component': return <Layers className="h-4 w-4" />;
      case 'utility': return <Settings className="h-4 w-4" />;
      default: return <Code className="h-4 w-4" />;
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-green-600/20 text-green-300 border-green-500/30';
      case 'POST': return 'bg-blue-600/20 text-blue-300 border-blue-500/30';
      case 'PUT': return 'bg-yellow-600/20 text-yellow-300 border-yellow-500/30';
      case 'DELETE': return 'bg-red-600/20 text-red-300 border-red-500/30';
      default: return 'bg-gray-600/20 text-gray-300 border-gray-500/30';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 border-blue-500/30 backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-8 w-8 text-blue-400" />
              <div>
                <CardTitle className="text-white text-2xl">{app.name} Blueprint</CardTitle>
                <CardDescription className="text-slate-300">
                  Complete technical specification and architecture
                </CardDescription>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={onViewCode}
                variant="outline"
                className="border-blue-500/50 text-blue-300 hover:bg-blue-900/30"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Code
              </Button>
              <Button
                onClick={onDownloadBlueprint}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Blueprint Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 bg-black/30 border border-slate-700">
          <TabsTrigger value="overview" className="data-[state=active]:bg-blue-600/30">
            Overview
          </TabsTrigger>
          <TabsTrigger value="components" className="data-[state=active]:bg-blue-600/30">
            Components
          </TabsTrigger>
          <TabsTrigger value="data" className="data-[state=active]:bg-blue-600/30">
            Data Models
          </TabsTrigger>
          <TabsTrigger value="api" className="data-[state=active]:bg-blue-600/30">
            API Endpoints
          </TabsTrigger>
          <TabsTrigger value="flow" className="data-[state=active]:bg-blue-600/30">
            User Flow
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Architecture Overview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-slate-300">{blueprint.architecture}</p>
              
              <Separator className="bg-slate-700" />
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-blue-400 mb-3">Tech Stack</h4>
                  <div className="flex flex-wrap gap-2">
                    {app.techStack.map((tech, index) => (
                      <Badge key={index} className="bg-blue-600/20 text-blue-300 border-blue-500/30">
                        {tech}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-green-400 mb-3">Key Features</h4>
                  <div className="space-y-2">
                    {app.features.slice(0, 5).map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-400" />
                        <span className="text-slate-300 text-sm">{feature.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <Separator className="bg-slate-700" />

              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-400">{blueprint.components.length}</div>
                  <div className="text-slate-400">Components</div>
                </div>
                <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                  <div className="text-2xl font-bold text-green-400">{blueprint.dataModels.length}</div>
                  <div className="text-slate-400">Data Models</div>
                </div>
                <div className="text-center p-3 bg-slate-800/50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-400">{blueprint.apiEndpoints.length}</div>
                  <div className="text-slate-400">API Endpoints</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Components Tab */}
        <TabsContent value="components" className="space-y-4">
          <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Layers className="h-5 w-5" />
                Component Architecture
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {blueprint.components.map((component, index) => (
                    <Card key={index} className="bg-slate-800/30 border-slate-600/50">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getComponentIcon(component.type)}
                            <h4 className="font-semibold text-white">{component.name}</h4>
                            <Badge className={`text-xs ${
                              component.type === 'page' ? 'bg-blue-600/20 text-blue-300' :
                              component.type === 'component' ? 'bg-green-600/20 text-green-300' :
                              'bg-purple-600/20 text-purple-300'
                            }`}>
                              {component.type}
                            </Badge>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyToClipboard(component.name)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                        
                        <p className="text-slate-300 text-sm mb-3">{component.description}</p>
                        
                        {component.props && component.props.length > 0 && (
                          <div className="mb-2">
                            <span className="text-xs text-slate-400">Props: </span>
                            <span className="text-xs text-slate-300">{component.props.join(', ')}</span>
                          </div>
                        )}
                        
                        <div>
                          <span className="text-xs text-slate-400">Dependencies: </span>
                          <span className="text-xs text-slate-300">{component.dependencies.join(', ')}</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Data Models Tab */}
        <TabsContent value="data" className="space-y-4">
          <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Database className="h-5 w-5" />
                Data Models
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {blueprint.dataModels.map((model, index) => (
                    <Card key={index} className="bg-slate-800/30 border-slate-600/50">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-white">{model.name}</h4>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyToClipboard(model.name)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                        
                        <div className="space-y-2 mb-3">
                          <h5 className="text-sm font-medium text-green-400">Fields:</h5>
                          {model.fields.map((field, fieldIndex) => (
                            <div key={fieldIndex} className="flex items-center justify-between text-sm bg-slate-900/50 p-2 rounded">
                              <div className="flex items-center gap-2">
                                <span className="text-white font-mono">{field.name}</span>
                                <Badge className="text-xs bg-blue-600/20 text-blue-300">
                                  {field.type}
                                </Badge>
                                {field.required && (
                                  <Badge className="text-xs bg-red-600/20 text-red-300">
                                    required
                                  </Badge>
                                )}
                              </div>
                              <span className="text-slate-400 text-xs">{field.description}</span>
                            </div>
                          ))}
                        </div>
                        
                        {model.relationships.length > 0 && (
                          <div>
                            <h5 className="text-sm font-medium text-purple-400 mb-1">Relationships:</h5>
                            <div className="flex flex-wrap gap-1">
                              {model.relationships.map((rel, relIndex) => (
                                <Badge key={relIndex} className="text-xs bg-purple-600/20 text-purple-300">
                                  {rel}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* API Endpoints Tab */}
        <TabsContent value="api" className="space-y-4">
          <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Globe className="h-5 w-5" />
                API Endpoints
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {blueprint.apiEndpoints.map((endpoint, index) => (
                    <Card key={index} className="bg-slate-800/30 border-slate-600/50">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-3">
                            <Badge className={`text-xs font-mono ${getMethodColor(endpoint.method)}`}>
                              {endpoint.method}
                            </Badge>
                            <code className="text-white font-mono text-sm">{endpoint.path}</code>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyToClipboard(`${endpoint.method} ${endpoint.path}`)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                        
                        <p className="text-slate-300 text-sm mb-3">{endpoint.description}</p>
                        
                        <div className="grid md:grid-cols-2 gap-3 text-sm">
                          {endpoint.requestBody && (
                            <div>
                              <span className="text-blue-400 font-medium">Request:</span>
                              <code className="block text-slate-300 font-mono text-xs mt-1 p-2 bg-slate-900/50 rounded">
                                {endpoint.requestBody}
                              </code>
                            </div>
                          )}
                          {endpoint.responseBody && (
                            <div>
                              <span className="text-green-400 font-medium">Response:</span>
                              <code className="block text-slate-300 font-mono text-xs mt-1 p-2 bg-slate-900/50 rounded">
                                {endpoint.responseBody}
                              </code>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Flow Tab */}
        <TabsContent value="flow" className="space-y-4">
          <Card className="bg-black/30 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Flow
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {blueprint.userFlow.map((step, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {step.step}
                      </div>
                      
                      <Card className="flex-1 bg-slate-800/30 border-slate-600/50">
                        <CardContent className="p-4">
                          <h4 className="font-semibold text-white mb-1">{step.title}</h4>
                          <p className="text-slate-300 text-sm mb-2">{step.description}</p>
                          
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-xs text-slate-400">Screen:</span>
                            <Badge className="text-xs bg-purple-600/20 text-purple-300">
                              {step.screen}
                            </Badge>
                          </div>
                          
                          <div>
                            <span className="text-xs text-slate-400">Actions:</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {step.actions.map((action, actionIndex) => (
                                <Badge key={actionIndex} className="text-xs bg-green-600/20 text-green-300">
                                  {action}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                      
                      {index < blueprint.userFlow.length - 1 && (
                        <div className="flex-shrink-0 mt-4">
                          <ArrowRight className="h-5 w-5 text-slate-400" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AppBlueprint;
